/**
 * 合同管理相关类型定义
 */

// 合同状态枚举
export enum ContractStatus {
  NEW = 'NEW', // 新建
  PENDING_SUPPLIER = 'PENDING_SUPPLIER', // 待供应商确认
  EFFECTIVE = 'EFFECTIVE', // 合同生效
  INVALID = 'INVALID', // 已失效
  REVOKED = 'REVOKED', // 已作废
  SUPPLIER_REJECTED = 'SUPPLIER_REJECTED', // 供应商退回
}

// 审批状态枚举
export enum ApprovalStatus {
  TO_APPROVE = 'TO_APPROVE', // 待审批
  APPROVING = 'APPROVING', // 审批中
  APPROVE = 'APPROVE', // 审批通过
  APPROVE_REJECT = 'APPROVE_REJECT', // 审批驳回
  APPROVE_REVOKE = 'APPROVE_REVOKE', // 审批撤回
}

// 合同类型枚举
export enum ContractType {
  PURCHASE = 'PURCHASE', // 采购合同
  SERVICE = 'SERVICE', // 服务合同
  CONSTRUCTION = 'CONSTRUCTION', // 工程合同
  LEASE = 'LEASE', // 租赁合同
  CONSULTING = 'CONSULTING', // 咨询合同
}

// 合同清单项接口
export interface ContractItem {
  id?: number; // 投标报价明细ID
  contractQuantity?: number; // 合同数量
  contractAmount?: number; // 合同总价
  demandLineNo?: string; // 需求行号
  materialCode?: string; // 物料编码
  materialName?: string; // 物料名称
  specModel?: string; // 规格型号
  unit?: string; // 单位
  demandRanch?: string; // 需求牧场
  ranchArea?: string; // 牧场地区
  detailedAddress?: string; // 详细地址
  taxRate?: number; // 税率
  unitPrice?: number; // 单价
  remark?: string; // 备注
}

// 合同附件接口
export interface ContractAttachment {
  fileName: string; // 文件名
  filePath: string; // 文件路径
  fileSize?: number; // 文件大小
  uploadTime?: string; // 上传时间
}

// 合同表单数据接口（根据图片中的字段定义）
export interface ContractFormData {
  // 基础信息
  contractName?: string; // 合同名称
  projectId?: string; // 采购立项ID
  sectionId?: string; // 标段ID
  purchaserDeptId?: string; // 采购方ID
  purchaserDeptName?: string; // 采购方名称
  purchaseContactId?: number; // 采购联系人ID
  purchaseContactPhone?: string; // 采购联系电话
  effectStartDate?: string; // 合同有效期起始时间
  effectEndDate?: string; // 合同有效期终止时间
  signDate?: string; // 签约时间
  signLocation?: string; // 签约地点
  paymentMethodId?: number; // 支付方式ID
  paymentPeriodId?: number; // 账期ID
  performanceBond?: number; // 履约保证金
  totalAmount?: number; // 合同含税总金额
  electronicSeal?: number; // 是否为电子章(0-否、1-是)
  
  // 供应商信息
  tenantSupplierId?: number; // 供应商ID
  supplierName?: string; // 供应商名称
  supplierSalesPrincipal?: string; // 销售负责人
  supplierSalesPrincipalPhone?: string; // 销售联系电话
  supplierBankCardId?: number; // 收款银行账户ID
  supplierBankName?: string; // 收款银行
  supplierBankAccount?: string; // 收款银行账号
  
  // 合同内容
  contractItems?: ContractItem[]; // 合同清单
  contractAttachment?: ContractAttachment[]; // 合同附件
  contractBody?: string; // 合同正文

  // 审批设置
  approvalType?: number; // 审批类型(0-系统自动发起、1-指定审批人)
  specialProcessExecutorList?: string[]; // 指定审批人列表
  
  // 扩展字段（用于表单显示）
  projectName?: string; // 项目名称（显示用）
  sectionName?: string; // 标段名称（显示用）
  purchaserContact?: string; // 采购联系人（显示用）
  paymentMethod?: string; // 支付方式（显示用）
  paymentTerm?: string; // 账期时间（显示用）
  isElectronicSignature?: boolean; // 是否电子签章（显示用）
  salesContact?: string; // 销售负责人（显示用）
  salesPhone?: string; // 销售联系电话（显示用）
  receivingBank?: string; // 收款银行（显示用）
  receivingAccount?: string; // 收款银行账号（显示用）
  template?: string; // 模板ID
}

// 合同列表项接口（根据接口文档）
export interface ContractListItem {
  id?: string;
  projectName: string; // 项目名称
  sectionName: string; // 标段名称
  contractCode: string; // 合同编号
  contractName: string; // 合同名称
  signDate: string; // 签约时间
  totalAmount: number; // 合同含税总金额
  purchaserDeptId?: string; // 采购方ID
  purchaseDeptName: string; // 采购方名称
  supplierName: string; // 供应商名称
  contractStatus: ContractStatus; // 合同状态
  approvalStatus: ApprovalStatus; // 审批状态
  createBy: string; // 创建人
  createByName: string; // 创建人名称
  createTime: string; // 创建时间
  updateBy: string; // 修改人
  updateByName: string; // 修改人名称
  updateTime: string; // 修改时间
  supplierRejectedContractInfo: string
  processInstanceType: 'SRM_TENDER_CONTRACT_REVOKE_AUDIT' | 'SRM_TENDER_CONTRACT_AUDIT'
}

// 合同查询参数接口（根据接口文档）
export interface ContractQueryParams {
  contractName?: string; // 合同名称
  supplierName?: string; // 供应商
  purchaserDeptName?: string; // 采购方
  signDate?: string; // 签约时间
  projectName?: string; // 项目名称
  supplierIds?: string[]; // 供应商ID数组
  sectionId?: string; // 标段ID
  projectId?: string; // 项目ID
  current?: number; // 当前页
  size?: number; // 每页大小
}

// 合同列表响应接口（根据接口文档）
export interface ContractListResponse {
  code?: number;
  bizCode?: string;
  msg?: string | null;
  data: {
    records: ContractListItem[]; // 合同列表
    total: number; // 总数
    size: number; // 每页大小
    current: number; // 当前页
    pages?: number; // 总页数
    orders?: any[]; // 排序信息
  };
}

// 标段信息接口
export interface SectionInfo {
  id: string;
  sectionCode: string; // 标段编号
  sectionName: string; // 标段名称
  projectId: string; // 所属项目ID
  status: string; // 标段状态
}

// 项目信息接口
export interface ProjectInfo {
  id: string;
  projectCode: string; // 项目编号
  projectName: string; // 项目名称
  projectType: string; // 项目类型
  status: string; // 项目状态
}

// 采购方信息接口
export interface PurchaserInfo {
  id: string;
  purchaserCode: string; // 采购方编号
  purchaserName: string; // 采购方名称
  purchaserType: string; // 采购方类型
  status: string; // 状态
}

// 供应商信息接口
export interface SupplierInfo {
  id: string;
  supplierCode: string; // 供应商编号
  supplierName: string; // 供应商名称
  supplierType: string; // 供应商类型
  status: string; // 状态
}

// 合同数据结构接口（保留原有结构用于其他功能）
export interface ContractData {
  id: string;
  contractNo: string; // 合同编号
  contractName: string; // 合同名称
  contractType: ContractType; // 合同类型
  projectId: string; // 项目ID
  projectInfo: ProjectInfo; // 项目信息
  sectionId: string; // 标段ID
  sectionInfo: SectionInfo; // 标段信息
  purchaserId: string; // 采购方ID
  purchaserInfo: PurchaserInfo; // 采购方信息
  supplierId: string; // 供应商ID
  supplierInfo: SupplierInfo; // 供应商信息
  totalAmount: number; // 含税合同总金额
  currency: string; // 币种
  signDate: string; // 签约时间
  contractStatus: ContractStatus; // 合同状态
  approvalStatus?: ApprovalStatus; // 审批状态
  creator?: string; // 创建人
  createTime?: string; // 创建时间
  modifier?: string; // 修改人
  modifyTime?: string; // 修改时间
}

// 合同创建参数接口
export interface ContractCreateParams {
  contractName: string; // 合同名称
  contractType: ContractType; // 合同类型
  projectId: string; // 项目ID
  sectionId: string; // 标段ID
  purchaserId: string; // 采购方ID
  supplierId: string; // 供应商ID
  totalAmount: number; // 合同金额
  currency: string; // 币种
  signDate: string; // 签约时间
}

// 合同更新参数接口
export interface ContractUpdateParams {
  contractName?: string; // 合同名称
  contractType?: ContractType; // 合同类型
  projectId?: string; // 项目ID
  sectionId?: string; // 标段ID
  purchaserId?: string; // 采购方ID
  supplierId?: string; // 供应商ID
  totalAmount?: number; // 合同金额
  currency?: string; // 币种
  signDate?: string; // 签约时间
}

// 合同状态更新参数接口
export interface ContractStatusUpdateParams {
  contractStatus: ContractStatus; // 合同状态
  remark?: string; // 备注
}

// 合同审批参数接口
export interface ContractApprovalParams {
  approvalStatus: ApprovalStatus; // 审批状态
  approvalOpinion?: string; // 审批意见
  remark?: string; // 备注
}

// 合同导出参数接口
export interface ContractExportParams {
  contractName?: string; // 合同名称
  supplierName?: string; // 供应商
  purchaserDeptName?: string; // 采购方
  signDate?: string; // 签约时间
  projectName?: string; // 项目名称
  contractStatus?: string; // 合同状态
  approvalStatus?: string; // 审批状态
  exportType: 'excel' | 'pdf'; // 导出类型
}

// 合同导入结果接口
export interface ContractImportResult {
  success: boolean; // 是否成功
  message: string; // 结果消息
  successCount: number; // 成功数量
  failCount: number; // 失败数量
  errors?: string[]; // 错误信息列表
} 