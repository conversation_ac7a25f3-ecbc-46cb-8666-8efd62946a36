/**
 * 合同管理相关API接口
 */
import request from '@/utils/request';
import type {
  ContractQueryParams,
  ContractListResponse,
  ContractFormData,
  ContractItem,
  ContractListItem
} from '@/types/contract';

// 获取合同列表（新接口）
export function getContractList(params: ContractQueryParams): Promise<ContractListResponse> {
  // 如果后端接口就绪，使用真实API
  return request({
    url: '/admin/srmTenderContract/query/page',
    method: 'post',
    data: params
  });
}

// 获取合同详情
export function getContractDetail(id: string, isPurchaser: boolean): Promise<{ code: number; data: ContractFormData; msg: string }> {
  return request({
    url: `/admin/srmTenderContract/detail/${id}?isPurchaser=${isPurchaser}`,
    method: 'get',
  });
}

export function getContractOldDetail(id: string): Promise<{ code: number; data: ContractFormData; msg: string }> {
  return request({
    url: `/admin/srmTenderContract/queryOldContract/${id}`,
    method: 'get',
  });
}

// 创建合同
export function createContract(data: ContractFormData): Promise<{ code: number; data: any; msg: string }> {
  return request({
    url: '/admin/srmTenderContract/save',
    method: 'post',
    data,
  });
}

// 更新合同
export function updateContract(data: ContractFormData): Promise<{ code: number; data: any; msg: string }> {
  return request({
    url: '/admin/srmTenderContract/save',
    method: 'post',
    data,
  });
}


// 获取中标物料列表
export function getAwardedMaterials(params: {
  projectId: string;
  sectionId: string;
  supplierId: string;
}): Promise<{ code: number; data: ContractItem[]; msg: string }> {
  return request({
    url: '/admin/srmTenderContract/awarded-materials',
    method: 'get',
    params,
  });
}

// 搜索供应商
export function searchSuppliers(query: string): Promise<{ code: number; data: any[]; msg: string }> {
  return request({
    url: '/admin/supplier/search',
    method: 'get',
    params: { query },
  });
}

// 获取合同模板列表
export function getContractTemplates(params?: { type?: string }): Promise<{ code: number; data: any[]; msg: string }> {
  return request({
    url: '/admin/contract/templates',
    method: 'get',
    params,
  });
}

// 获取合同模板详情
export function getContractTemplateDetail(id: string): Promise<{ code: number; data: any; msg: string }> {
  return request({
    url: `/admin/contract/template/${id}`,
    method: 'get',
  });
}


// 合同退回
export function rejectContract(contractId: string, reason?: string): Promise<{ code: number; data: any; msg: string }> {
  return request({
    url: `/admin/srmTenderContract/reject/${contractId}`,
    method: 'put',
    data: { reason },
  });
}

// 合同确认
export function confirmContract(contractId: string): Promise<{ code: number; data: any; msg: string }> {
  return request({
    url: `/admin/srmTenderContract/confirmSign/${contractId}`,
    method: 'put',
  });
}

// 撤回审批
export function revokeContractApproval(contract: ContractListItem): Promise<{ code: number; data: any; msg: string }> {
  return request({
    url: `/admin/processManagement/stopProcess`,
    method: 'post',
    data: {
      bizType: contract.processInstanceType,
      bizKey: contract.contractCode,
      args: [contract.contractCode, contract.contractCode]
    }
  });
}

// 作废合同
export function cancelContract(data: { bizType: string, bizKey: string, args: string[] }): Promise<{ code: number; data: any; msg: string }> {
  return request({
    url: `/admin/processManagement/startProcess`,
    method: 'post',
    data,
  });
}

/**
 * 获取中标供应商列表
 * @param noticeId 公告ID
 * @param projectId 项目ID
 * @param params 请求参数
 */
export function getUnSignedAwardedSuppliers(
  noticeId: number,
  projectId: number,
  params?: any
) {
  return request({
    url: `/admin/srmTenderContract/awardedSuppliers`,
    method: 'post',
    data: {
      noticeId,
      projectId,
      ...params,
    },
  });
}

// 合同变更相关API

/**
 * 获取合同变更列表
 * @param params 查询参数
 */
export function getContractChangeList(params: any): Promise<{ code: number; data: { records: any[]; total: number }; msg: string }> {
  return request({
    url: '/admin/noticeChange/page',
    method: 'post',
    data: params,
  });
}



/**
 * 删除合同变更
 * @param id 变更记录ID
 */
export function deleteContractChange(id: string): Promise<{ code: number; data: any; msg: string }> {
  return request({
    url: `/admin/srmTenderContract/change/delete/${id}`,
    method: 'delete',
  });
}

/**
 * 审批合同变更
 * @param id 变更记录ID
 * @param action 审批动作 (approve/reject)
 * @param reason 审批意见
 */
export function approveContractChange(id: string, action: 'approve' | 'reject', reason?: string): Promise<{ code: number; data: any; msg: string }> {
  return request({
    url: `/admin/srmTenderContract/change/approve/${id}`,
    method: 'put',
    data: {
      action,
      reason,
    },
  });
}

