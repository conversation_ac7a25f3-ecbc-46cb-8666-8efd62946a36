import request from '/@/utils/request';

/**
 * 分页查询变更列表
 */
export function noticeChangePageApi(data?: Object, params?: Object) {
  return request({
    url: '/admin/noticeChange/page',
    method: 'post',
    data,
    params,
  });
}

/**
 * 查询变更详情
 */
export function getNoticeChangeDetail(id: number) {
  return request({
    url: `/admin/noticeChange/${id}`,
    method: 'get',
  });
}

/**
 * 新增变更
 */
export function addNoticeChange(data?: Object) {
  return request({
    url: '/admin/noticeChange/add',
    method: 'post',
    data,
  });
}

/**
 * 更新变更
 */
export function updateNoticeChange(data?: Object) {
  return request({
    url: '/admin/noticeChange/add',
    method: 'post',
    data,
  });
}


/**
 * 查询变更公告详情
 */
export function getNoticeChangeDetailInfo(data: {
  noticeId: string | number;
  projectId: string | number;
  changeTypeEnum: string;
}) {
  return request({
    url: '/admin/noticeChange/detail',
    method: 'post',
    data,
  });
} 