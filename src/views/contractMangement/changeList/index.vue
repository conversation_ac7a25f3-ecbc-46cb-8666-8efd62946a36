<template>
  <div class="change-list-container">
    <yun-pro-table
      ref="proTableRef"
      v-model:pagination="pagination"
      v-model:searchData="searchData"
      :search-fields="searchFields"
      :layout="'whole'"
      :auto-height="true"
      :table-columns="tableColumns"
      :remote-method="remoteMethod"
      :table-props="tableProps"
    >
      <template #tableHeaderLeft>
        <el-button
          type="primary"
          @click="handleAddChange"
        >
          <el-icon><Plus /></el-icon>
          新建变更
        </el-button>
      </template>
      <template #t_approvedStatus="{ row }">
        <el-tag
          :type="getApprovalStatusType(row.approvedStatus)"
          size="small"
        >
          {{ getApprovalStatusText(row.approvedStatus) }}
        </el-tag>
      </template>
      <template #t_action="{ row }">
        <yun-rest limit="3">
          <el-button
            type="text"
            size="small"
            @click="handleView(row)"
          >
            查看变更合同详情
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="handleViewBeforeContract(row)"
          >
            查看变更前合同详情
          </el-button>
          <el-button
            v-if="canEditChange(row)"
            type="text"
            size="small"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="canRevokeChange(row)"
            type="text"
            size="small"
            @click="handleRevoke(row)"
          >
            撤回
          </el-button>
        </yun-rest>
      </template>
    </yun-pro-table>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Plus } from '@element-plus/icons-vue';
import { useContractStore } from '@/stores/contract';
import { useProTable } from '@ylz-use/core';
import { ElMessage, ElMessageBox } from 'yun-design';
import { getContractChangeList } from '@/api/contract';
import { cancelApprovePlan } from '@/api/purchasing/plan';

// 路由相关
const route = useRoute();
const router = useRouter();
const contractStore = useContractStore();

// 获取合同ID和项目名称
const contractId = computed(() => route.query.id as string);
const projectName = computed(() => route.query.projectName as string);
const contractCode = computed(() => route.query.contractCode as string);

// 搜索字段配置
const searchFields = computed(() => [
  {
    label: '变更人',
    prop: 'changeBy',
    component: 'el-input',
    componentAttrs: {
      placeholder: '请输入变更人',
      clearable: true,
    },
  },
  {
    label: '变更时间',
    prop: 'changeTime',
    component: 'el-date-picker',
    componentAttrs: {
      type: 'date',
      placeholder: '请选择变更时间',
      clearable: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      style: 'width: 100%',
    },
  },
  {
    label: '审核状态',
    prop: 'approvedStatus',
    component: 'el-select',
    componentAttrs: {
      placeholder: '请选择审核状态',
      clearable: true,
      style: 'width: 100%',
    },
    options: [
      { label: '待审批', value: 'TO_APPROVE' },
      { label: '审批中', value: 'APPROVING' },
      { label: '审批通过', value: 'APPROVE' },
      { label: '审批驳回', value: 'APPROVE_REJECT' },
      { label: '审批撤销', value: 'APPROVE_REVOKE' },
    ],
  },
]);

// 表格列配置
const tableColumns = computed(() => [
  {
    label: '序号',
    type: 'index',
  },
  {
    label: '项目名称',
    prop: 'projectName',
    showOverflowTooltip: true,
  },
  {
    label: '变更人',
    prop: 'changeByName',
  },
  {
    label: '变更时间',
    prop: 'changeTime',
  },
  {
    label: '变更类型',
    prop: 'changeType',
    render: (row: any) => {
      return '合同变更';
    },
  },
  {
    label: '审批状态',
    prop: 'approvedStatus',
    slot: true,
  },
  {
    label: '操作',
    prop: 'action',
    fixed: 'right',
    width: 320,
    slot: true,
  },
]);

// 表格属性配置
const tableProps = computed(() => ({
  stripe: true,
  border: true,
  'show-summary': false,
}));

// 搜索数据
const searchData = ref({});

// 使用yun-pro-table
const { pagination, remoteMethod, proTableRef, reLoad } = useProTable({
  apiFn: getContractChangeList,
  paramsHandler(params: any) {
    return {
      current: pagination.value.page,
      size: pagination.value.size || pagination.value.pageSize,
      bizId: contractCode.value,
      ...params,
    };
  },
  responseHandler(result: any) {
    return result.data;
  },
  plugins: {
    config: {
      columns: tableColumns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});

// 审批状态相关方法
const getApprovalStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    TO_APPROVE: 'info',
    APPROVING: 'warning',
    APPROVE: 'success',
    APPROVE_REJECT: 'danger',
    APPROVE_REVOKE: 'info',
  };
  return statusMap[status] || 'info';
};

const getApprovalStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    TO_APPROVE: '待审批',
    APPROVING: '审批中',
    APPROVE: '审批通过',
    APPROVE_REJECT: '审批驳回',
    APPROVE_REVOKE: '审批撤销',
  };
  return statusMap[status] || '-';
};

// 权限判断方法
const canEditChange = (row: any): boolean => {
  // 审批驳回、审批撤销可以编辑
  return ['APPROVE_REJECT', 'APPROVE_REVOKE'].includes(row.approvedStatus);
};

const canRevokeChange = (row: any): boolean => {
  // 待审批、审批中可以撤销
  return ['TO_APPROVE', 'APPROVING'].includes(row.approvedStatus);
};

// 事件处理方法
const handleAddChange = () => {
  contractStore.setShouldResetForm(true);
  router.push({
    path: '/contractMangement/changeForm',
    query: {
      mode: 'create',
      id: contractId.value,
      projectName: projectName.value,
      type: 'change',
    },
  });
};

const handleView = (row: any) => {
  // 查看变更合同详情
  router.push({
    path: '/contractMangement/changeView',
    query: {
      mode: 'view',
      id: contractCode.value,
      projectName: projectName.value,
      type: 'change',
    },
  });
};

const handleViewBeforeContract = (row: any) => {
  router.push({
    path: '/contractMangement/view',
    query: {
      mode: 'view',
      id: contractId.value,
      projectName: projectName.value,
    },
  });
};

const handleEdit = (row: any) => {
  // 编辑变更
  router.push({
    path: '/contractMangement/changeEdit',
    query: {
      mode: 'edit',
      id: contractCode.value,
      projectName: projectName.value,
      type: 'change',
    },
  });
};

// 撤销审批
const handleRevoke = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要撤销审批吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    await cancelApprovePlan({
      bizType: 'SRM_CHANGE_AUDIT',
      bizKey: row.id,
    });
    reLoad();
    ElMessage({
      type: 'success',
      message: '撤销审批成功！',
    });
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤销审批失败:', error);
      ElMessage.error('撤销审批失败');
    }
  }
};

// 监听合同ID变化，重新加载数据
watch(contractId, (newContractId, oldContractId) => {
  if (newContractId && newContractId !== oldContractId && route.path.includes('contractMangement/changeList')) {
    reLoad();
  }
});
</script>

<style scoped lang="scss">
.change-list-container {
  width: 100%;
  height: calc(100vh - 88px);
  display: flex;
  flex-direction: column;
  .text-red-500 {
    color: #ef4444;
  }

  .flex {
    display: flex;
  }

  .gap-2 {
    gap: 0.5rem;
  }
}
</style>
