<template>
  <div class="contract-form-page">
    <div class="page-content">
      <ContractForm
        ref="contractFormRef"
        :mode="mode"
        :type="type"
        :contractId="contractId"
        :projectName="projectName"
        :is-purchaser="isPurchaser"
        :is-supplier="isSupplier"
        @success="handleSuccess"
        @cancel="handleCancel"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'yun-design';
import ContractForm from './components/ContractForm.vue';
import { useUserRole } from '@/views/procurementSourcing/biddingProcess/utils';

// 路由相关
const route = useRoute();
const router = useRouter();

// 用户角色判断
const { isPurchaser, isSupplier } = useUserRole();

// 从路由参数获取模式和数据
const mode = computed(() => (route.query.mode as 'create' | 'edit' | 'view') || 'create');
const contractId = computed(() => (route.query.id as string) || '');
const projectName = computed(() => (route.query.projectName as string) || '');
const type = computed(() => (route.query.type as 'change' | '') || '');

// 引用 ContractForm 组件
const contractFormRef = ref<InstanceType<typeof ContractForm>>();

// 监听 contractId 变更，调用 ContractForm 的 init 方法
watch(contractId, (newContractId, oldContractId) => {
  const currentPath = route.path;
  const allowedPaths = [
    '/contractMangement/changeEdi',
    '/contractMangement/changeView', 
    '/contractMangement/view',
    '/contractMangement/edit'
  ];
  
  if (newContractId !== oldContractId && contractFormRef.value && allowedPaths.some(path => currentPath.includes(path))) {
    contractFormRef.value.init();
  }
});

// 事件处理
const handleSuccess = () => {
  ElMessage.success('操作成功');
};

const handleCancel = () => {
  handleBack();
};

const handleBack = () => {
  router.back();
};
</script>

<style lang="scss" scoped>
.contract-form-page {
  position: relative;
  background-color: #f9fafb;
  height: calc(100vh - 84px);
  background: #f0f2f5;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.page-content {
  flex: 1;
}
</style>
