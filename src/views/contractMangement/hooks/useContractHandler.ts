/**
 * 合同操作处理Hook
 */
import { ref } from 'vue';
import { ElMessage, ElMessageBox } from 'yun-design';
import type { ContractListItem } from '@/types/contract';
import { ContractStatus, ApprovalStatus } from '@/types/contract';
import { useRouter } from 'vue-router';
import { revokeContractApproval, confirmContract, rejectContract, cancelContract } from '@/api/contract';
import { useContractStore } from '@/stores/contract';

export function useContractHandler(isPurchaser: boolean) {
  const router = useRouter();
  const contractStore = useContractStore();

  // 状态管理
  const revokingContract = ref(false);
  const cancelingContract = ref(false);
  // 合同状态相关方法
  const getContractStatusType = (status: ContractStatus) => {
    const statusMap = {
      [ContractStatus.NEW]: 'info',
      [ContractStatus.PENDING_SUPPLIER]: 'warning',
      [ContractStatus.EFFECTIVE]: 'success',
      [ContractStatus.INVALID]: 'danger',
      [ContractStatus.REVOKED]: 'danger',
      [ContractStatus.SUPPLIER_REJECTED]: 'danger',
    };
    return statusMap[status] || 'info';
  };

  const getContractStatusText = (status: ContractStatus) => {
    const statusMap = {
      [ContractStatus.NEW]: '新建',
      [ContractStatus.PENDING_SUPPLIER]: '待供应商确认',
      [ContractStatus.EFFECTIVE]: '合同生效',
      [ContractStatus.INVALID]: '已失效',
      [ContractStatus.REVOKED]: '已作废',
      [ContractStatus.SUPPLIER_REJECTED]: '供应商退回',
    };
    return statusMap[status] || '-';
  };

  // 审批状态相关方法
  const getApprovalStatusType = (status: ApprovalStatus) => {
    const statusMap = {
      [ApprovalStatus.TO_APPROVE]: 'info',
      [ApprovalStatus.APPROVING]: 'warning',
      [ApprovalStatus.APPROVE]: 'success',
      [ApprovalStatus.APPROVE_REJECT]: 'danger',
      [ApprovalStatus.APPROVE_REVOKE]: 'danger',
    };
    return statusMap[status] || 'info';
  };

  const getApprovalStatusText = (status: ApprovalStatus) => {
    const statusMap = {
      [ApprovalStatus.TO_APPROVE]: '待审批',
      [ApprovalStatus.APPROVING]: '审批中',
      [ApprovalStatus.APPROVE]: '审批通过',
      [ApprovalStatus.APPROVE_REJECT]: '审批驳回',
      [ApprovalStatus.APPROVE_REVOKE]: '审批撤回',
    };
    return statusMap[status] || '-';
  };

  // 事件处理方法
  const handleCreate = () => {
    // 设置需要重置表单的状态
    contractStore.setShouldResetForm(true);
    
    router.push({
      path: '/contractMangement/form',
      query: { mode: 'create' },
    });
  };

  const handleView = (row: ContractListItem) => {
    router.push({
      path: '/contractMangement/view',
      query: { 
        mode: 'view', 
        id: row.contractCode,
        projectName: row.projectName 
      },
    });
  };

  const handleEdit = (row: ContractListItem) => {
    router.push({
      path: '/contractMangement/edit',
      query: { 
        mode: 'edit', 
        id: row.contractCode,
        projectName: row.projectName 
      },
    });
  };

  const handleChange = (row: ContractListItem) => {
    router.push({
      path: '/contractMangement/changeList',
      query: { 
        mode: 'change', 
        id: row.contractCode,
        projectName: row.projectName,
        contractCode: row.id
      },
    });
  };

  // 判断是否可以编辑合同
  const canEditContract = (row: ContractListItem): boolean => {
    // 只有采购方可以编辑
    if (!isPurchaser) {
      return false;
    }

    if (row.approvalStatus === 'APPROVE' && ['SUPPLIER_REJECTED']?.includes(row.contractStatus)) {
      return true;
    }

    // 只有审批驳回或审批撤回的合同或供应商驳回才能编辑
    const editableStatuses = ['APPROVE_REJECT', 'APPROVE_REVOKE'];
    return editableStatuses.includes(row.approvalStatus || '');
  };

  // 判断是否可以撤回合同
  const canRevokeContract = (row: ContractListItem): boolean => {
    // 只有审批中的合同才能撤回
    return row.approvalStatus === 'APPROVING';
  };

  const canCancelContract = (row: ContractListItem): boolean => {
    // 只有采购方可以作废
    if (!isPurchaser) {
      return false;
    }
    return row.approvalStatus === 'APPROVE' && row.contractStatus !== 'REVOKED';
  };

  const canChangeContract = (row: ContractListItem): boolean => {
    // 只有采购方可以变更
    if (!isPurchaser) {
      return false;
    }
    return row.approvalStatus === 'APPROVE' && row.contractStatus === 'EFFECTIVE';
  };

  // 撤回合同审批
  const handleRevoke = async (row: ContractListItem, reLoad?: () => void) => {
    try {
      if (revokingContract.value === true) {
        return;
      }

      await ElMessageBox.confirm('确定要撤回该合同的审批吗？撤回后合同将回到待审批状态。', '确认撤回', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });

      revokingContract.value = true;
      const response = await revokeContractApproval(row);
      if (response.code === 0) {
        ElMessage.success('撤回成功');
        // 刷新列表
        if (reLoad) {
          reLoad();
        }
      } else {
        ElMessage.error(response.msg || '撤回失败');
      }
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('撤回失败');
      }
    } finally {
      revokingContract.value = false;
    }
  };


  const handleCancelContract = async (row: ContractListItem, reLoad?: () => void) => {
    try {
      if (cancelingContract.value === true) {
        return;
      }

      await ElMessageBox.confirm('确定要作废该合同吗？作废后该合同将失效。', '确认作废', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });

      const data = {
        bizType: 'SRM_TENDER_CONTRACT_REVOKE_AUDIT',
        bizKey: row.contractCode,
        args: [row.contractCode, row.contractCode]
      }

      cancelingContract.value = true;
      const response = await cancelContract(data);
      if (response.code === 0) {
        ElMessage.success('操作成功');
        // 刷新列表
        if (reLoad) {
          reLoad();
        }
      } else {
        ElMessage.error(response.msg || '作废失败');
      }
    } catch (error) {
      ElMessage.error('作废失败');
    } finally {
      cancelingContract.value = false;
    }
  };

  // 时间格式化方法
  const formatDate = (date: string | null | undefined) => {
    if (!date) return '--';
    return date.split(' ')[0]; // 只显示日期部分
  };

  const formatDateTime = (dateTime: string | null | undefined) => {
    if (!dateTime) return '--';
    return dateTime;
  };

  const formatSignDate = (date: string | null | undefined) => {
    if (!date) return '--';
    return date;
  };

  // 金额格式化
  const formatAmount = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined) return '--';
    return `${amount}元`;
  };

  // 刷新表格方法
  const refreshTable = (tableRef: any) => {
    if (tableRef.value?.getData) {
      tableRef.value.getData();
    }
  };

  return {
    // 状态相关方法
    getContractStatusType,
    getContractStatusText,
    getApprovalStatusType,
    getApprovalStatusText,

    // 事件处理方法
    handleCreate,
    handleView,
    handleEdit,
    handleRevoke,
    canEditContract,
    canRevokeContract,
    canCancelContract,
    handleCancelContract,
    canChangeContract,
    handleChange,

    // 状态变量
    revokingContract,
    cancelingContract,

    // 格式化方法
    formatDate,
    formatDateTime,
    formatSignDate,
    formatAmount,

    // 工具方法
    refreshTable,
  };
}