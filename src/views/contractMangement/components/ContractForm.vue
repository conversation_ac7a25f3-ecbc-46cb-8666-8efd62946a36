<template>
  <div class="contract-form-container">
    <div
      v-if="activeModule === 'form'"
      class="form-module"
    >
      <!-- 查看模式：使用 el-descriptions 展示详情 -->
      <div v-if="isViewMode">
        <!-- 基础信息 -->
        <div class="form-section">
          <div class="section-header">
            <div class="header-title">基础信息</div>
          </div>
          <div class="section-content">
            <el-descriptions :column="2">
              <el-descriptions-item label="合同名称">
                {{ contractForm.contractName || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="项目名称">
                {{ contractForm.projectName || '--' }}
              </el-descriptions-item>
              <el-descriptions-item
                label="标段名称"
                v-if="sectionOptions.length > 1"
              >
                {{ contractForm.sectionName || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="采购方">
                {{ contractForm.purchaserDeptName || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="采购联系人">
                {{ deptUserList.find((item: any) => item.userId === contractForm?.purchaseContactId?.toString())?.username || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="采购联系电话">
                {{ contractForm.purchaseContactPhone || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="合同有效期">
                {{ formatValidityPeriod() }}
              </el-descriptions-item>
              <el-descriptions-item label="签约时间">
                {{ contractForm.signDate || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="签约地点">
                {{ contractForm.signLocation || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="是否电子签章">
                {{ isElectronicSignature ? '是' : '否' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>

        <!-- 供方信息 -->
        <div class="form-section">
          <div class="section-header">
            <div class="header-title">供方信息</div>
          </div>
          <div class="section-content">
            <el-descriptions :column="2">
              <el-descriptions-item label="供应商名称">
                {{ contractForm.supplierName || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="销售负责人">
                {{ contractForm.supplierSalesPrincipal || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="销售联系电话">
                {{ contractForm.supplierSalesPrincipalPhone || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="收款银行">
                {{ contractForm.supplierBankName || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="收款银行账号">
                {{ contractForm.supplierBankAccount || '--' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>

        <!-- 支付信息 -->
        <div class="form-section">
          <div class="section-header">
            <div class="header-title">支付信息</div>
          </div>
          <div class="section-content">
            <el-descriptions :column="2">
              <el-descriptions-item label="履约保证金">
                {{ contractForm.performanceBond ? `${contractForm.performanceBond} 元` : '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="合同金额">
                {{ contractForm.totalAmount ? `${contractForm.totalAmount} 元` : '--' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>

        <!-- 合同附件 -->
        <div class="form-section">
          <div class="section-header">
            <div class="header-title">合同附件</div>
          </div>
          <div class="section-content">
            <el-descriptions :column="2">
              <el-descriptions-item label="引用模版">
                <div
                  class="inline-flex items-center"
                  style="gap: 10px"
                >
                  <span>{{ getTemplateName(contractForm.template) }}</span>
                  <el-button
                    type="text"
                    @click="handleViewContract"
                  >
                    <el-icon class="mr-[6px]"><edit-pen /></el-icon> 查看合同
                  </el-button>
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="合同附件">
                <div v-if="contractForm.contractAttachment && contractForm.contractAttachment.length > 0">
                  <div
                    v-for="(attachment, index) in contractForm.contractAttachment"
                    :key="index"
                    class="attachment-item"
                  >
                    <span class="file-name">{{ attachment.fileName }}</span>
                    <el-button
                      type="text"
                      size="small"
                      @click="handleDownloadAttachment(attachment)"
                    >
                      <el-icon><Download /></el-icon>
                    </el-button>
                  </div>
                </div>
                <span v-else>--</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </div>

      <!-- 查看模式：合同清单 -->
      <div
        v-if="isViewMode && renderMaterialList.length > 0"
        class="form-section"
      >
        <div class="section-header">
          <div class="header-title">合同清单</div>
        </div>
        <div class="section-content">
          <el-table
            :data="renderMaterialList"
            style="width: 100%"
            class="award-detail-table"
            row-key="id"
            :max-height="materialTableMaxHeight"
          >
            <template
              v-for="item in dynamicColumn"
              :key="item.prop || item.label"
            >
              <template v-if="item.children?.length">
                <el-table-column
                  :key="item.prop"
                  :label="item.label"
                  :prop="item.prop"
                  :min-width="item.width"
                  :show-overflow-tooltip="item.showOverflowTooltip"
                >
                  <el-table-column
                    v-for="child in item.children"
                    :key="child.prop"
                    :label="child.label"
                    :prop="child.prop"
                    :min-width="child.width"
                    :show-overflow-tooltip="child.showOverflowTooltip"
                  />
                </el-table-column>
              </template>
              <el-table-column
                v-else
                :key="item.prop || item.label"
                :label="item.label"
                :prop="item.prop"
                :min-width="item.width"
                :show-overflow-tooltip="item.showOverflowTooltip"
              >
              </el-table-column>
            </template>
            <el-table-column
              label="合同数量"
              prop="awardedQuantity"
              min-width="200"
              fixed="right"
            />
            <el-table-column
              label="支付方式"
              prop="projectPaymentId"
              min-width="200"
              fixed="right"
            >
              <template #default="{ row }">
                <div>{{ row.paymentList?.find((item: any) => item.value === row.procurementProjectPaymentId)?.label }}</div>
              </template>
            </el-table-column>
            <el-table-column
              label="合同金额"
              prop="bidAmount"
              min-width="200"
              fixed="right"
            >
              <template #default="{ row }">
                <div>
                  {{
                    precisionMath.multiply(
                      row.paymentList?.find((item: any) => item.value === row.procurementProjectPaymentId)?.price ?? 0,
                      row.awardedQuantity || 0
                    )
                  }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 编辑和新增模式：使用表单 -->
      <el-form
        v-if="!isViewMode"
        ref="formRef"
        :model="contractForm"
        :rules="formRules"
        label-width="140px"
      >
        <!-- 基础信息 -->
        <div class="form-section">
          <div class="section-header">
            <div class="header-title">基础信息</div>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item
                  label="合同名称"
                  prop="contractName"
                >
                  <el-input
                    v-model="contractForm.contractName"
                    placeholder="请输入"
                    maxlength="30"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="项目名称"
                  prop="projectId"
                >
                  <el-select
                    v-model="contractForm.projectId"
                    placeholder="可选择已定标的项目名称,支持模糊搜索"
                    filterable
                    remote
                    :remote-method="handleProjectSearch"
                    :loading="projectLoading"
                    @change="handleProjectChange"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="project in projectOptions"
                      :key="project.id"
                      :label="`${project.projectName} (${project.projectCode})`"
                      :value="project.id"
                    >
                      <div style="display: flex; align-items: center; gap: 4px">
                        <div>{{ project.projectName }}</div>
                        <div style="font-size: 12px; color: #999">
                          {{ project.projectCode }}
                        </div>
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                v-if="sectionOptions.length > 1"
                :span="12"
              >
                <el-form-item
                  label="标段名称"
                  prop="sectionId"
                >
                  <el-select
                    v-model="contractForm.sectionId"
                    placeholder="选择项目下已定标的标段,支持模糊搜索"
                    filterable
                    remote
                    :remote-method="handleSectionSearch"
                    :loading="sectionLoading"
                    @change="handleSectionChange"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="section in sectionOptions"
                      :key="section.id"
                      :label="`${section.sectionName} (${section.sectionCode})`"
                      :value="section.id"
                    >
                      <div style="display: flex; align-items: center; gap: 4px">
                        <div>{{ section.sectionName }}</div>
                        <div style="font-size: 12px; color: #999">
                          {{ section.sectionCode }}
                        </div>
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="采购方"
                  prop="purchaserDeptId"
                >
                  <el-cascader
                    v-model="contractForm.purchaserDeptId"
                    :options="treeDeptData"
                    filterable
                    clearable
                    placeholder="请选择采购方"
                    :props="{
                      value: 'id',
                      label: 'name',
                      children: 'children',
                      checkStrictly: true,
                      emitPath: false,
                      showAllLevels: false,
                    }"
                    @change="handlePurchaserDeptChange"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="采购联系人"
                  prop="purchaseContactId"
                >
                  <el-select
                    v-model="contractForm.purchaseContactId"
                    placeholder="请选择采购联系人"
                    clearable
                    filterable
                    style="width: 100%"
                    @change="handlePurchaserContactChange"
                  >
                    <el-option
                      v-for="user in deptUserList"
                      :key="user.id"
                      :label="user.username"
                      :value="user.userId"
                    >
                      <div style="display: flex; align-items: center; gap: 4px">
                        <div>{{ user.username }}</div>
                        <div style="font-size: 12px; color: #999">
                          {{ user.departmentPath || '--' }}
                        </div>
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="采购联系电话"
                  prop="purchaseContactPhone"
                >
                  <el-input
                    v-model="contractForm.purchaseContactPhone"
                    placeholder="请输入"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="合同有效期"
                  prop="validityPeriod"
                >
                  <el-date-picker
                    v-model="validityPeriod"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    style="width: 100%"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    @change="handleValidityPeriodChange"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="签约时间"
                  prop="signDate"
                >
                  <el-date-picker
                    v-model="contractForm.signDate"
                    type="datetime"
                    placeholder="请选择签约时间"
                    style="width: 100%"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="签约地点"
                  prop="signLocation"
                >
                  <el-input
                    v-model="contractForm.signLocation"
                    placeholder="请输入"
                    maxlength="30"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="是否电子签章"
                  prop="electronicSeal"
                >
                  <el-switch
                    v-model="isElectronicSignature"
                    active-text="是"
                    inactive-text="否"
                    @change="handleElectronicSealChange"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 供方信息 -->
        <div class="form-section">
          <div class="section-header">
            <div class="header-title">供方信息</div>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item
                  label="供应商名称"
                  prop="supplierName"
                >
                  <el-select
                    v-model="contractForm.supplierName"
                    placeholder="请选择中标供应商"
                    style="width: 100%"
                    filterable
                    :loading="supplierLoading"
                    @change="handleSupplierChange"
                  >
                    <el-option
                      v-for="supplier in supplierOptions"
                      :key="supplier.tenantSupplierId"
                      :label="supplier.supplierName"
                      :value="supplier.supplierName"
                    >
                      <div style="display: flex; align-items: center; gap: 4px">
                        <div>{{ supplier.supplierName }}</div>
                        <div style="font-size: 12px; color: #999">
                          {{ supplier.contactName }}
                        </div>
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="销售负责人"
                  prop="supplierSalesPrincipal"
                >
                  <el-input
                    v-model="contractForm.supplierSalesPrincipal"
                    placeholder="请输入"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item
                  label="销售联系电话"
                  prop="supplierSalesPrincipalPhone"
                >
                  <el-input
                    v-model="contractForm.supplierSalesPrincipalPhone"
                    placeholder="请输入"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="收款银行"
                  prop="supplierBankName"
                >
                  <el-input
                    v-model="contractForm.supplierBankName"
                    placeholder="请输入"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item
                  label="收款银行账号"
                  prop="supplierBankAccount"
                >
                  <el-input
                    v-model="contractForm.supplierBankAccount"
                    placeholder="请输入"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 合同清单 -->
        <div
          v-if="awardDetailList.length > 0"
          class="form-section"
        >
          <div class="section-header">
            <div class="header-title">合同清单</div>
          </div>
          <div class="section-content">
            <div class="material-actions">
              <el-button
                type="primary"
                @click="handleOpenMaterialSelector"
              >
                选择中标物料
              </el-button>
              <el-button
                type="danger"
                @click="handleBatchDeleteMaterials"
                :disabled="selectedMaterialIds.length === 0"
              >
                批量删除
              </el-button>
            </div>

            <el-table
              :data="renderMaterialList"
              style="width: 100%"
              class="award-detail-table"
              row-key="id"
              :max-height="materialTableMaxHeight"
              @selection-change="handleMaterialSelectionChange"
            >
              <el-table-column
                type="selection"
                width="55"
                fixed="left"
              />
              <template
                v-for="item in dynamicColumn"
                :key="item.prop || item.label"
              >
                <template v-if="item.children?.length">
                  <el-table-column
                    :key="item.prop"
                    :label="item.label"
                    :prop="item.prop"
                    :min-width="item.width"
                    :show-overflow-tooltip="item.showOverflowTooltip"
                  >
                    <el-table-column
                      v-for="child in item.children"
                      :key="child.prop"
                      :label="child.label"
                      :prop="child.prop"
                      :min-width="child.width"
                      :show-overflow-tooltip="child.showOverflowTooltip"
                    />
                  </el-table-column>
                </template>
                <el-table-column
                  v-else
                  :key="item.prop || item.label"
                  :label="item.label"
                  :prop="item.prop"
                  :min-width="item.width"
                  :show-overflow-tooltip="item.showOverflowTooltip"
                >
                </el-table-column>
              </template>
              <el-table-column
                label="合同数量"
                prop="awardedQuantity"
                min-width="200"
                fixed="right"
              >
                <template #default="{ row }">
                  <el-input
                    style="width: 100%"
                    placeholder="请输入"
                    v-model="row.awardedQuantity"
                    type="number"
                    :min="0.01"
                    :step="0.01"
                    :precision="2"
                    @input="handleAwardedQuantityInput($event, row)"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="支付方式"
                prop="projectPaymentId"
                min-width="200"
                fixed="right"
              >
                <template #default="{ row }">
                  <div>{{ row.paymentList?.find((item: any) => item.value === row.procurementProjectPaymentId)?.label }}</div>
                </template>
              </el-table-column>
              <el-table-column
                label="合同金额"
                prop="bidAmount"
                min-width="200"
                fixed="right"
              >
                <template #default="{ row }">
                  <div>
                    {{
                      precisionMath.multiply(
                        row.paymentList?.find((item: any) => item.value === row.procurementProjectPaymentId)?.price ?? 0,
                        row.awardedQuantity || 0
                      )
                    }}
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <div class="form-section">
          <div class="section-header">
            <div class="header-title">支付信息</div>
          </div>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item
                  label="履约保证金"
                  prop="performanceBond"
                >
                  <el-input
                    v-model="contractForm.performanceBond"
                    placeholder="请输入"
                    type="number"
                  >
                    <template #append>元</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="合同金额"> {{ contractForm.totalAmount ?? '--' }} 元 </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 合同附件 -->
        <div class="form-section">
          <div class="section-header">
            <div class="header-title">合同附件</div>
          </div>
          <div class="section-content">
            <el-row>
              <el-col :span="8">
                <el-form-item
                  label="引用模版"
                  v-if="props.mode !== 'view'"
                  prop="template"
                >
                  <el-select
                    v-model="contractForm.template"
                    placeholder="请选择公告模版"
                    @change="handleTemplateChange"
                  >
                    <el-option
                      v-for="template in templateOptions"
                      :key="template.id"
                      :label="template.templateName"
                      :value="template.id"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item
                  v-else
                  label="合同内容"
                >
                  <el-button
                    type="text"
                    @click="handleViewContract"
                    class="edit-button"
                  >
                    <el-icon class="mr-[6px]"><edit-pen /></el-icon>
                    查看
                  </el-button>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="合同附件">
                  <!-- 编辑模式：显示上传组件 -->
                  <div class="upload-wrapper">
                    <YunUpload
                      v-model="contractForm.contractAttachment"
                      :limit="5"
                      :multiple="true"
                      @change="handleAttachmentChange"
                    ></YunUpload>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 审批设置 -->
        <div
          class="form-section"
          v-if="props.mode !== 'view'"
        >
          <div class="section-header">
            <div class="header-title">审批设置</div>
          </div>
          <div class="section-content">
            <el-row>
              <el-col :span="24">
                <el-form-item
                  label="发起审批"
                  prop="specialProcessExecutorList"
                >
                  <div class="approval-wrapper flex items-center">
                    <el-radio-group
                      v-model="contractForm.approvalType"
                      @change="handleApprovalTypeChange"
                      class="approval-radio-group"
                    >
                      <el-radio :label="0">系统自动发起</el-radio>
                      <el-radio :label="1">指定审批人</el-radio>
                    </el-radio-group>
                    <UserSelector
                      style="width: auto !important"
                      v-if="contractForm.approvalType === 1"
                      v-model="contractForm.specialProcessExecutorList"
                      :multiple="true"
                      placeholder="请选择审批人"
                      @update:model-value="handleApprovalDataChange"
                    />
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
    </div>

    <!-- 合同内容展示模块 -->
    <div
      v-if="activeModule === 'content'"
      class="content-module"
    >
      <div class="content-header">
        <div class="template-selector">
          <el-button
            type="text"
            @click="handleBack"
          >
            <el-icon><ArrowLeftBold /></el-icon>
            <span>上一步</span>
          </el-button>
        </div>
        <div class="content-actions">
          <el-button
            type="text"
            @click="handlePrint"
            >打印</el-button
          >
          <el-button
            type="text"
            @click="handleDownload"
            >下载</el-button
          >
        </div>
      </div>
      <div class="content-body">
        <div class="contract-content">
          <div
            v-if="isSupplier"
            v-html="contractForm.contractBody"
            class="content-preview"
          ></div>
          <div
            v-else
            class="flex-1 overflow-y-auto"
          >
            <div class="w-full min-h-[400px]">
              <div class="w-full h-full rich-editor-container bg-[#fff]">
                <Toolbar
                  :editor="editorRef"
                  :defaultConfig="toolbarConfig"
                  mode="default"
                />
                <Editor
                  v-model="contractForm.contractBody"
                  :defaultConfig="editorConfig"
                  mode="default"
                  @onCreated="handleCreated"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div
      v-if="!isViewMode"
      class="form-actions-wrapper"
    >
      <div class="form-actions">
        <!-- 采购方 - 表单模式 -->
        <el-button
          v-if="isPurchaser && activeModule === 'form' && props.mode === 'create'"
          type="primary"
          @click="handleGenerateContract"
        >
          生成合同
        </el-button>

        <el-button
          v-if="isPurchaser && activeModule === 'form' && props.mode === 'edit'"
          type="primary"
          @click="handleGenerateContract"
        >
          生成合同
        </el-button>

        <!-- 采购方 - 合同内容模式 -->
        <el-button
          v-if="isPurchaser && activeModule === 'content'"
          @click="handleCancel"
        >
          取消
        </el-button>

        <el-button
          v-if="isPurchaser && activeModule === 'content' && props.mode !== 'view'"
          type="primary"
          @click="handleSubmit"
        >
          提交
        </el-button>

        <!-- 供应商 - 表单模式 -->
        <el-button
          v-if="activeModule === 'form' && props.mode === 'view'"
          type="primary"
          @click="handleViewContract"
        >
          查看合同
        </el-button>
      </div>
    </div>

    <div
      v-if="isSupplier && contractForm.contractStatus === 'PENDING_SUPPLIER'"
      class="form-actions-wrapper"
    >
      <el-button
        :loading="rejectingContract"
        :disabled="rejectingContract"
        @click="handleContractReject"
      >
        合同退回
      </el-button>
      <el-button
        type="primary"
        :loading="confirmingContract"
        :disabled="confirmingContract"
        @click="handleContractConfirm"
      >
        合同确认
      </el-button>
    </div>
  </div>

  <!-- 物料选择模态框 -->
  <MaterialSelector
    v-model:visible="materialSelectorVisible"
    :available-materials="getAvailableMaterials()"
    :dynamic-columns="dynamicColumn"
    @confirm="handleConfirmMaterialSelection"
  />
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, shallowRef, watch, onActivated } from 'vue';
import { useRouter } from 'vue-router';
import '@wangeditor/editor/dist/css/style.css';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import YunUpload from '@/components/YunUpload/index.vue';
import UserSelector from '@/components/UserSelector/index.vue';
import type { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor';
import { ElMessage, ElMessageBox } from 'yun-design';
import type { FormInstance, FormRules } from 'element-plus';
import { ArrowLeftBold, Download } from '@element-plus/icons-vue';
import { useWindowSize } from '@vueuse/core';
import type { ContractFormData, ContractAttachment } from '@/types/contract';
import { getContractDetail, getContractOldDetail, createContract, updateContract, rejectContract, confirmContract } from '@/api/contract';
import { useContractStore } from '@/stores/contract';
import { useProjectSelector } from '../hooks/useProjectSelector';
import { getProjectSections } from '@/views/procurementSourcing/biddingProcess/api/bidding';
import { getObj } from '@/api/admin/user';
import { deptTree, getObj as getDeptObj } from '@/api/admin/dept';
import { getUserListApi } from '@/api/purchasing/plan';
import { getEffectNotice } from '@/api/purchasing/bid';
import { getUnSignedAwardedSuppliers } from '@/api/contract';
import { getAwardedSuppliers } from '@/views/procurementSourcing/biddingProcess/api/WinnerAnnouncement';
import { getTemplateList } from '@/views/procurementSourcing/biddingProcess/api/award';
import { useDynamicTable } from '@/views/procurementSourcing/biddingProcess/hooks/useDynamicTable.jsx';
import { getAwardedItems } from '@/views/procurementSourcing/biddingProcess/api/WinnerPublicity';
import { useBiddingStore, useAwardStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { getTemplateDetail } from '@/views/procurementSourcing/biddingProcess/api/announcement';
import { getBankAccountsBySupplierIdPage } from '@/api/lowcode/supply';
import { templateContentFormatter } from '@/views/procurementSourcing/biddingProcess/components/announcement/ProcurementDocument/templateFormatter';
import Decimal from 'decimal.js';
import moment from 'moment';
import MaterialSelector from './MaterialSelector.vue';
import { useUserRole } from '@/views/procurementSourcing/biddingProcess/utils';
import downloadUrlFile from '@/utils/downloadUrl.js';
import { addNoticeChange, updateNoticeChange, getNoticeChangeDetailInfo } from '@/api/purchasing/noticeChange';

const { isPurchaser, isSupplier } = useUserRole();

// 使用合同 store
const contractStore = useContractStore();

const precisionMath = {
  // 乘法
  multiply: (a: number | string, b: number | string): number => {
    return new Decimal(a || 0).mul(new Decimal(b || 0)).toNumber();
  },
  // 加法
  add: (a: number | string, b: number | string): number => {
    return new Decimal(a || 0).add(new Decimal(b || 0)).toNumber();
  },
  // 减法
  subtract: (a: number | string, b: number | string): number => {
    return new Decimal(a || 0).sub(new Decimal(b || 0)).toNumber();
  },
  // 除法
  divide: (a: number | string, b: number | string): number => {
    if (Number(b) === 0) return 0;
    return new Decimal(a || 0).div(new Decimal(b)).toNumber();
  },
  // 保留指定位数小数
  toFixed: (value: number | string, decimals: number = 2): string => {
    return new Decimal(value || 0).toFixed(decimals);
  },
};

// 定义组件 Props
interface Props {
  mode: 'create' | 'edit' | 'view'; // 组件状态
  contractId?: string; // 合同ID（编辑和查看时传入）
  projectName?: string; // 项目名称
  isPurchaser?: boolean; // 是否为采购方
  isSupplier?: boolean; // 是否为供应商
  type?: 'change' | ''; // 合同类型
}

const router = useRouter();
const { getProjectDetailData } = useBiddingStore();

const awardStore = useAwardStore();
const { dynamicColumn, tableData, setDynamicColumn, handleTableData, objectSpanMethod } = useDynamicTable();

const props = withDefaults(defineProps<Props>(), {
  mode: 'create',
  contractId: '',
  projectName: '',
  isPurchaser: false,
  isSupplier: false,
});

const emit = defineEmits<{
  (e: 'success'): void;
  (e: 'cancel'): void;
}>();

const editorRef = shallowRef<IDomEditor>();

const toolbarConfig: Partial<IToolbarConfig> = {
  excludeKeys: ['fullScreen'],
};
const editorConfig: Partial<IEditorConfig> = {
  placeholder: '请输入合同内容...',
};

// 使用项目选择hook
const {
  projectOptions,
  selectedProject,
  loading: projectLoading,
  getProjectList,
  setSelectedProject,
  handleProjectChange: hookHandleProjectChange,
  handleProjectSearch: hookHandleProjectSearch,
} = useProjectSelector(props.mode, props.type);

// 响应式数据
const activeModule = ref<'form' | 'content'>('form');
const formRef = ref<FormInstance>();
const submitting = ref(false);
const confirmingContract = ref(false);
const rejectingContract = ref(false);

// 部门树数据
const treeDeptData = ref([]);

// 部门人员列表
const deptUserList = ref<any[]>([]);

// 获取部门树数据
const getDeptTree = async () => {
  try {
    const resp = await deptTree();
    treeDeptData.value = resp.data;
  } catch (error) {
    treeDeptData.value = [];
  }
};

// 获取树形数据中的项目
const getTreeItem = (tree: any[], val: string | number, config?: { value?: string; children?: string }): any => {
  const { value = 'value', children = 'children' } = config || {};
  let item = {};
  for (let i = 0; i < tree.length; i += 1) {
    if (tree[i][value] === val) {
      item = tree[i];
      break;
    } else {
      if (tree[i]?.[children]?.length) {
        item = getTreeItem(tree[i][children], val, config);
        if (Object.keys(item).length) {
          break;
        }
      }
    }
  }
  return item;
};

// 获取部门人员列表
const getDeptUserList = async (deptId: string) => {
  try {
    const response = await getUserListApi([deptId]);
    if (response.code === 0 && response.data) {
      deptUserList.value = response.data;
    } else {
      deptUserList.value = [];
    }
  } catch (error) {
    deptUserList.value = [];
  }
};

// 处理采购部门选择变化
const handlePurchaserDeptChange = async (value: string) => {
  if (value) {
    const selectedDept = getTreeItem(treeDeptData.value, value, {
      value: 'id',
      children: 'children',
    });

    if (selectedDept) {
      // 设置采购方相关信息
      contractForm.purchaserDeptId = selectedDept.id;
      contractForm.purchaserDeptName = selectedDept.name || '';
      // 获取部门人员列表
      await getDeptUserList(selectedDept.id);
    }
  } else {
    // 清空相关字段
    contractForm.purchaserDeptId = '';
    contractForm.purchaserDeptName = '';
    contractForm.purchaserContact = '';
    contractForm.purchaseContactPhone = '';
    deptUserList.value = [];
  }
};

// 处理采购联系人选择变化
const handlePurchaserContactChange = (value: string) => {
  if (value) {
    // 根据选中的用户名找到对应的用户信息
    const selectedUser = deptUserList.value.find((user) => user.userId === value);
    if (selectedUser) {
      // 自动设置采购联系电话
      contractForm.purchaseContactPhone = selectedUser.phone || '';
    }
  } else {
    // 清空采购联系电话
    contractForm.purchaseContactPhone = '';
  }
};

// 表单数据 - 使用新的数据结构
const contractForm = reactive<ContractFormData>({
  // 基础信息
  contractName: '',
  projectId: undefined,
  sectionId: undefined,
  purchaserDeptId: '',
  purchaserDeptName: '',
  purchaseContactId: undefined,
  purchaseContactPhone: '',
  effectStartDate: '',
  effectEndDate: '',
  signDate: '',
  signLocation: '',
  paymentMethodId: undefined,
  paymentPeriodId: undefined,
  performanceBond: undefined,
  totalAmount: undefined,
  electronicSeal: 0,

  // 供应商信息
  tenantSupplierId: undefined,
  supplierName: '',
  supplierSalesPrincipal: '',
  supplierSalesPrincipalPhone: '',
  supplierBankName: '',
  supplierBankAccount: '',

  // 合同内容
  contractItems: [],
  contractAttachment: [],
  contractBody: '',

  // 审批设置
  approvalType: 0,
  specialProcessExecutorList: [] as string[],

  // 扩展字段（用于表单显示）
  projectName: '',
  sectionName: '',
  purchaserContact: '',
  paymentMethod: '',
  paymentTerm: '',
  isElectronicSignature: false,
  salesContact: '',
  salesPhone: '',
  receivingBank: '',
  receivingAccount: '',
  template: '',
});

// 合同内容相关
const templateOptions = ref<any[]>([]);

// 有效期（用于日期选择器）
const validityPeriod = ref<string[]>([]);

// 中标明细列表（总量数据）
const awardDetailList = ref<any[]>([]);

// 渲染的物料列表（用于表格显示）
const renderMaterialList = ref<any[]>([]);

// 选中的物料ID列表（用于批量删除）
const selectedMaterialIds = ref<number[]>([]);

// 选择物料模态框状态
const materialSelectorVisible = ref(false);

// 是否电子签章（用于开关组件）
const isElectronicSignature = computed({
  get: () => contractForm.electronicSeal === 1,
  set: (value: boolean) => {
    contractForm.electronicSeal = value ? 1 : 0;
  },
});

// 计算属性
const isViewMode = computed(() => props.mode === 'view');

// 合同清单表格高度（随窗口变化自适应），超过高度滚动
const { height: windowHeight } = useWindowSize();
const materialTableMaxHeight = computed(() => {
  const reserved = 420; // 预留给其它区域
  const minHeight = 260;
  return Math.max(minHeight, windowHeight.value - reserved);
});

// 计算合同总金额
const contractTotalAmount = computed(() => {
  return renderMaterialList.value.reduce((total, row) => {
    const price = row.paymentList?.find((item: any) => item.value === row.procurementProjectPaymentId)?.price ?? 0;
    const quantity = row.awardedQuantity || 0;
    const rowAmount = precisionMath.multiply(price, quantity);
    return precisionMath.add(total, rowAmount);
  }, 0);
});

// 表单验证规则
const formRules: FormRules = {
  contractName: [
    { required: true, message: '请输入合同名称', trigger: 'blur' },
    { max: 30, message: '合同名称不能超过30个字符', trigger: 'blur' },
  ],
  projectId: [{ required: true, message: '请选择项目名称', trigger: 'change' }],
  sectionId: [{ required: true, message: '请选择标段名称', trigger: 'change' }],
  purchaserDeptId: [{ required: true, message: '请选择采购方', trigger: 'change' }],
  purchaserContact: [{ required: true, message: '请输入采购联系人', trigger: 'blur' }],
  purchaseContactPhone: [{ required: true, message: '请输入采购联系电话', trigger: 'blur' }],
  signDate: [{ required: true, message: '请选择签约时间', trigger: 'change' }],
  signLocation: [{ required: true, message: '请输入签约地点', trigger: 'blur' }],
  supplierName: [{ required: true, message: '请选择供应商名称', trigger: 'change' }],
  supplierSalesPrincipal: [{ required: true, message: '请输入销售负责人', trigger: 'blur' }],
  supplierSalesPrincipalPhone: [{ required: true, message: '请输入销售联系电话', trigger: 'blur' }],
  template: [{ required: true, message: '请选择引用模版', trigger: 'change' }],
  specialProcessExecutorList: [
    {
      required: false,
      validator: (rule: any, value: any, callback: any) => {
        if (contractForm.approvalType === 1 && (!contractForm.specialProcessExecutorList || contractForm.specialProcessExecutorList.length === 0)) {
          callback(new Error('请选择审批人'));
        } else {
          callback();
        }
      },
      trigger: 'change',
    },
  ],
};

function handleCreated(editor: IDomEditor) {
  editorRef.value = editor;
}

// 处理有效期变化
const handleValidityPeriodChange = (dates: string[]) => {
  if (dates && dates.length === 2) {
    contractForm.effectStartDate = dates[0];
    contractForm.effectEndDate = dates[1];
  } else {
    contractForm.effectStartDate = '';
    contractForm.effectEndDate = '';
  }
};

// 处理电子签章变化
const handleElectronicSealChange = (value: boolean) => {
  contractForm.electronicSeal = value ? 1 : 0;
};

// 标段选项
const sectionOptions = ref<any[]>([]);
const sectionLoading = ref(false);

// 中标供应商相关
const allAwardedSuppliers = ref<any[]>([]); // 所有标段的中标供应商
const supplierOptions = ref<any[]>([]); // 当前标段过滤后的供应商选项
const supplierLoading = ref(false);

// 选中的标段和供应商
const selectedSection = ref<any>(null);
const selectedSupplier = ref<any>(null);

// 模板详情内容
const templateContent = ref<string>('');

// 公告信息
const noticeInfo = ref<any>(null);

// 获取生效公告数据
async function getEffectNoticeData() {
  const projectId = selectedProject.value?.id;
  if (!projectId) {
    return;
  }
  try {
    const res = await getEffectNotice(projectId);
    noticeInfo.value = res?.data || null;
  } catch (error) {
    noticeInfo.value = null;
  }
}

// 获取中标供应商数据
const getAwardedSuppliersData = async (projectId: string, noticeId: number) => {
  if (!projectId || !noticeId) return;

  supplierLoading.value = true;
  try {
    const server = props.mode === 'create' && !props.type ? getUnSignedAwardedSuppliers : getAwardedSuppliers;
    const response = await server(noticeInfo.value?.id || 0, Number(projectId), {});

    if (response.code === 0) {
      allAwardedSuppliers.value = response.data || [];

      // 获取中标供应商后，重新过滤标段信息
      await filterSectionsByAwardedSuppliers();

      // 根据当前选中的标段过滤供应商
      filterSuppliersBySection();
    }
  } catch (error) {
    allAwardedSuppliers.value = [];
    sectionOptions.value = [];
  } finally {
    supplierLoading.value = false;
  }
};

// 根据中标供应商过滤标段信息
const filterSectionsByAwardedSuppliers = async () => {
  if (!selectedProject.value?.id) return;

  try {
    const response = await getProjectSections(selectedProject.value.id);
    if (response.code === 0) {
      const allProjectSections = response.data || [];

      // 从中标供应商列表中提取唯一的标段ID
      const awardedSectionIds = [...new Set(allAwardedSuppliers.value.map((supplier) => supplier.sectionId).filter(Boolean))];

      // 过滤出中标供应商对应的标段
      sectionOptions.value = allProjectSections.filter((section: any) => awardedSectionIds.includes(section.id));

      // 特殊处理：当标段数据长度等于1时，直接设置选中
      if (sectionOptions.value.length === 1) {
        const section = sectionOptions.value[0];
        contractForm.sectionId = section.id;
        contractForm.sectionName = section.sectionName;

        // 标段自动选中后，重新过滤供应商
        filterSuppliersBySection();
      }
    }
  } catch (error) {
    sectionOptions.value = [];
  }
};

// 根据标段过滤供应商
const filterSuppliersBySection = () => {
  if (!contractForm.sectionId) {
    supplierOptions.value = [];
    return;
  }

  // 根据标段ID过滤供应商
  supplierOptions.value = allAwardedSuppliers.value.filter((supplier) => supplier.sectionId === contractForm.sectionId);
};

// 获取采购部门信息
const getPurchaseDeptInfo = async (userId: string) => {
  try {
    const response = await getObj(userId);
    if (response.code === 0 && response.data) {
      const deptInfo = response.data;
      // 设置采购方相关信息
      contractForm.purchaserDeptId = deptInfo.deptId;
      contractForm.purchaserDeptName = deptInfo.deptName || ''; // 部门名称

      // 获取部门人员列表
      if (deptInfo.deptId) {
        await getDeptUserList(deptInfo.deptId);
      }
    }
  } catch (error) {}
};

// 清除关联字段数据
const clearRelatedFields = () => {
  // 清除标段相关数据
  contractForm.sectionId = undefined;
  contractForm.sectionName = '';
  sectionOptions.value = [];

  // 清除采购方相关数据
  contractForm.purchaserDeptId = '';
  contractForm.purchaserDeptName = '';
  contractForm.purchaserContact = '';
  contractForm.purchaseContactPhone = '';
  deptUserList.value = [];

  // 清除供应商相关数据
  contractForm.tenantSupplierId = undefined;
  contractForm.supplierName = '';
  contractForm.supplierSalesPrincipal = '';
  contractForm.supplierSalesPrincipalPhone = '';
  contractForm.supplierBankName = '';
  contractForm.supplierBankAccount = '';

  // 清除中标供应商数据
  allAwardedSuppliers.value = [];
  supplierOptions.value = [];

  // 清除合同清单数据
  contractForm.contractItems = [];

  // 清除合同正文（因为项目变更可能影响合同内容）
  contractForm.contractBody = '';

  noticeInfo.value = null;
};

const handleAwardedQuantityInput = (value: string | number, row: any) => {
  const numValue = Number(value);

  // 验证输入值
  if (numValue <= 0) {
    ElMessage.warning('中标数量必须大于0');
    row.awardedQuantity = '';
    return;
  }

  const { projectPaymentId, paymentList } = row;
  const payment = paymentList.find((item: any) => item.value === projectPaymentId);
  // 如果输入有效，自动计算中标价格
  if (row.projectPaymentId) {
    row.bidAmount = precisionMath.multiply(numValue, payment.price ?? 0);
  }
};

// 处理项目名称变化
const handleProjectChange = (value: string) => {
  const project = projectOptions.value.find((item) => item.id === value);
  if (project) {
    contractForm.projectId = project.id;
    contractForm.projectName = project.projectName;
    setSelectedProject(project);

    getProjectDetailData({ projectCode: project.projectCode });

    // 清除关联字段数据
    clearRelatedFields();

    // 项目变更时，先获取公告信息，再获取中标供应商数据（标段信息从中标供应商中提取）
    getEffectNoticeData().then(async () => {
      // 公告信息获取完成后，获取中标供应商数据
      await getAwardedSuppliersData(project.id, noticeInfo.value?.id);

      const projectLeader = project?.projectMemberList?.find((item: any) => item.role === 'PROJECT_LEADER');

      // 如果项目有采购部门ID，获取部门信息
      if (project.purchaseDeptId && projectLeader) {
        await getPurchaseDeptInfo(projectLeader?.userId);
      }
    });
  }
};

// 处理标段选择变化
const handleSectionChange = async (value: string) => {
  const section = sectionOptions.value.find((item) => item.id === value);
  if (section) {
    contractForm.sectionId = section.id;
    contractForm.sectionName = section.sectionName;

    // 标段变更时，重新过滤供应商
    filterSuppliersBySection();

    await awardStore.loadForAwardResult(noticeInfo.value?.id || 0, Number(selectedProject.value?.id) || 0, true);

    // 清除供应商相关数据
    contractForm.tenantSupplierId = undefined;
    contractForm.supplierName = '';
    contractForm.supplierSalesPrincipal = '';
    contractForm.supplierSalesPrincipalPhone = '';
    contractForm.supplierBankName = '';
    contractForm.supplierBankAccount = '';
  }
};

// 获取供应商银行账户并自动填充最新的银行信息
const getSupplierBankAccounts = async (supplierId: number) => {
  if (!supplierId) {
    return;
  }

  try {
    const response = await getBankAccountsBySupplierIdPage({
      supplierId,
      current: 1,
      size: 100, // 获取银行账户
    });

    if (response.code === 0 && response.data?.records?.length > 0) {
      // 按ID排序，获取最新的银行账户信息
      const sortedAccounts = response.data.records.sort((a: any, b: any) => b.id - a.id);
      const latestAccount = sortedAccounts[0];

      // 自动填充最新的银行账户信息
      contractForm.supplierBankName = latestAccount.bankName;
      contractForm.supplierBankAccount = latestAccount.accountNo;
    }
  } catch (error) {
    console.warn('获取银行账户失败:', error);
  }
};

// 处理供应商变化
const handleSupplierChange = (value: string) => {
  const supplier = supplierOptions.value.find((item) => item.supplierName === value);
  if (supplier) {
    contractForm.tenantSupplierId = supplier.tenantSupplierId;
    contractForm.supplierName = supplier.supplierName;
    contractForm.supplierSalesPrincipal = supplier.contactName || '';
    contractForm.supplierSalesPrincipalPhone = supplier.contactPhone || '';

    // 获取物料数据
    getAwardDetailData();

    // 异步获取并自动填充最新的银行账户信息
    getSupplierBankAccounts(supplier.tenantSupplierId);
  }
};

// 处理审批类型变化
const handleApprovalTypeChange = (value: number) => {
  contractForm.approvalType = value;
  if (value === 0) {
    contractForm.specialProcessExecutorList = [];
  }
};

// 处理审批数据变化
const handleApprovalDataChange = (value: string[] | string) => {
  if (Array.isArray(value)) {
    contractForm.specialProcessExecutorList = value;
  } else {
    contractForm.specialProcessExecutorList = value ? [value] : [];
  }
};

// 处理附件变化
const handleAttachmentChange = (files: ContractAttachment[]) => {
  contractForm.contractAttachment = files;
};

const handleDownloadAttachment = (attachment: ContractAttachment) => {
  downloadUrlFile(attachment.filePath, attachment.fileName);
};

const handleProjectSearch = async (query: string) => {
  await hookHandleProjectSearch(query);
};

const handleSectionSearch = async (query: string) => {
  if (query) {
    sectionOptions.value = [];
  }
};

const handleTemplateChange = async () => {
  if (!contractForm.template) return;

  try {
    const response = await getTemplateDetail(contractForm.template);
    if (response.code === 0) {
      templateContent.value = response.data.content;
    }
  } catch (error) {}
};

const handleViewContract = () => {
  activeModule.value = 'content';
};

const handlePrint = () => {
  if (!contractForm.contractBody) {
    ElMessage.warning('合同内容为空，无法打印');
    return;
  }

  // 创建隐藏的iframe用于打印
  const printFrame = document.createElement('iframe');
  printFrame.style.position = 'fixed';
  printFrame.style.right = '0';
  printFrame.style.bottom = '0';
  printFrame.style.width = '0';
  printFrame.style.height = '0';
  printFrame.style.border = '0';
  printFrame.style.opacity = '0';

  document.body.appendChild(printFrame);

  // 构建打印内容
  const printContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>合同打印</title>
      <style>
        body {
          font-family: 'Microsoft YaHei', Arial, sans-serif;
          line-height: 1.6;
          margin: 20px;
          color: #333;
        }
        .contract-content {
          text-align: justify;
        }
        @media print {
          body { margin: 0; }
        }
      </style>
    </head>
    <body>
      <div class="contract-content">${contractForm.contractBody}</div>
    </body>
    </html>
  `;

  printFrame.contentDocument?.write(printContent);
  printFrame.contentDocument?.close();

  // 等待内容加载完成后自动打印并移除iframe
  printFrame.onload = () => {
    printFrame.contentWindow?.print();

    // 打印完成后移除iframe
    setTimeout(() => {
      if (document.body.contains(printFrame)) {
        document.body.removeChild(printFrame);
      }
    }, 1000);
  };
};

const handleDownload = () => {
  if (!contractForm.contractBody) {
    ElMessage.warning('合同内容为空，无法下载');
    return;
  }

  // 创建下载内容
  const downloadContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>${contractForm.contractName || '合同'}</title>
      <style>
        body {
          font-family: 'Microsoft YaHei', Arial, sans-serif;
          line-height: 1.6;
          margin: 20px;
          color: #333;
        }
        .contract-header {
          text-align: center;
          margin-bottom: 30px;
          font-size: 18px;
          font-weight: bold;
        }
        .contract-content {
          text-align: justify;
        }
      </style>
    </head>
    <body>
      <div class="contract-content">${contractForm.contractBody}</div>
    </body>
    </html>
  `;

  // 创建 Blob 对象
  const blob = new Blob([downloadContent], { type: 'text/html;charset=utf-8' });

  // 创建下载链接
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `${contractForm.contractName || '合同'}_${new Date().toISOString().slice(0, 10)}.html`;

  // 触发下载
  document.body.appendChild(link);
  link.click();

  // 清理
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

const handleBack = () => {
  activeModule.value = 'form';
};

const handleGenerateContract = async () => {
  try {
    // 1. 先验证表单
    if (!formRef.value) return;

    const valid = await formRef.value.validate();
    if (!valid) {
      ElMessage.error('请完善表单信息');
      return;
    }

    // 2. 检查是否选择了模板
    if (!contractForm.template) {
      ElMessage.error('请先选择合同模板');
      return;
    }

    // 3. 转换合同数据为模板格式
    const templateData = transformContractDataForTemplate();

    // 4. 获取模板内容（如果还没有的话）
    if (!templateContent.value || templateContent.value.includes('<h2>XDMY-LYMG')) {
      if (!contractForm.template) {
        ElMessage.error('请先选择合同模板');
        return;
      }
      const { data } = await getTemplateDetail(String(contractForm.template));
      templateContent.value = data.content;
    }

    // 5. 使用模板格式化器处理内容
    const formattedContent = templateContentFormatter(templateContent.value, templateData, {
      defaultValue: '-',
      keepUndefinedVariables: false,
      removeEmptyLoops: true,
      removeEmptyTags: false,
      debug: true, // 调试模式，便于查看
    });

    // 6. 更新合同正文内容
    contractForm.contractBody = formattedContent;

    // 7. 切换到合同正文查看组件
    activeModule.value = 'content';
  } catch (error) {}
};

// 数据转换函数 - 将合同数据转换为模板格式
function transformContractDataForTemplate() {
  // 使用计算属性中的合同总金额
  const totalAmount = contractTotalAmount.value;

  return {
    // 合同基本信息
    contractName: contractForm.contractName || '',
    contractCode: `HT-${new Date().getTime()}`,
    projectName: contractForm.projectName || '',
    projectCode: selectedProject.value?.projectCode || '',
    sectionName: contractForm.sectionName || '',
    sectionCode: selectedSection.value?.sectionCode || '',

    // 甲方信息（采购方）
    purchaserDeptName: contractForm.purchaserDeptName || '',
    purchaserDeptId: contractForm.purchaserDeptId || '',

    // 乙方信息（供应商）
    supplierName: contractForm.supplierName || '',
    supplierCode: selectedSupplier.value?.supplierCode || '',
    contactPerson: selectedSupplier.value?.contactPerson || '',
    contactPhone: selectedSupplier.value?.contactPhone || '',

    // 合同金额信息
    totalAmount: totalAmount,
    formattedTotalAmount: formatPrice(totalAmount),
    paymentMethod: contractForm.paymentMethod || 'ONE_TIME',
    paymentMethodText: contractForm.paymentMethod === 'ONE_TIME' ? '一次性付款' : '分期付款',

    // 时间信息
    signDate: contractForm.signDate || '',
    signLocation: contractForm.signLocation || '',
    effectStartDate: contractForm.effectStartDate || '',
    effectEndDate: contractForm.effectEndDate || '',
    generateTime: moment().format('YYYY-MM-DD HH:mm:ss'),
    currentDate: moment().format('YYYY年MM月DD日'),

    // 合同物料清单
    contractMaterials: renderMaterialList.value.map((item: any, index: number) => {
      const price = item.paymentList?.find((payment: any) => payment.value === item.procurementProjectPaymentId)?.price ?? 0;
      const quantity = item.awardedQuantity || item.contractQuantity || 0;
      const amount = precisionMath.multiply(price, quantity);

      return {
        index: index + 1,
        materialCode: item.materialCode,
        materialName: item.materialName,
        specModel: item.specModel,
        unit: item.unit,
        requiredQuantity: item.requiredQuantity,
        contractQuantity: quantity,
        quotePrice: price,
        contractAmount: amount,
        formattedPrice: formatPrice(price),
        formattedAmount: formatPrice(amount),
        remark: item.remark || '',
      };
    }),

    // 统计信息
    materialCount: renderMaterialList.value.length,
    totalQuantity: renderMaterialList.value.reduce(
      (sum: number, item: any) => precisionMath.add(sum, item.awardedQuantity || item.contractQuantity || 0),
      0
    ),
  };
}

// 格式化价格
function formatPrice(price: number): string {
  return `¥${price.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

// 供应商合同退回
const handleContractReject = async () => {
  try {
    if (rejectingContract.value === true) {
      return;
    }

    await ElMessageBox.confirm('确定要退回合同吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    rejectingContract.value = true;
    const response = await rejectContract(contractForm.id || '');
    if (response.code === 0) {
      ElMessage.success('合同退回成功');
      // 返回上一级路由
      router.go(-1);
    } else {
      ElMessage.error(response.msg || '合同退回失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('合同退回失败');
    }
  } finally {
    rejectingContract.value = false;
  }
};

// 供应商合同确认
const handleContractConfirm = async () => {
  try {
    if (confirmingContract.value === true) {
      return;
    }

    await ElMessageBox.confirm('确定要确认合同吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    confirmingContract.value = true;
    const response = await confirmContract(contractForm.id || '');
    if (response.code === 0) {
      ElMessage.success('合同确认成功');
      // 返回上一级路由
      router.go(-1);
    } else {
      ElMessage.error(response.msg || '合同确认失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('合同确认失败');
    }
  } finally {
    confirmingContract.value = false;
  }
};

// 重置表单数据
const resetForm = () => {
  // 重置基础表单数据
  Object.assign(contractForm, {
    // 基础信息
    contractName: '',
    projectId: undefined,
    sectionId: undefined,
    purchaserDeptId: '',
    purchaserDeptName: '',
    purchaseContactId: undefined,
    purchaseContactPhone: '',
    effectStartDate: '',
    effectEndDate: '',
    signDate: '',
    signLocation: '',
    paymentMethodId: undefined,
    paymentPeriodId: undefined,
    performanceBond: undefined,
    totalAmount: undefined,
    electronicSeal: 0,

    // 供应商信息
    tenantSupplierId: undefined,
    supplierName: '',
    supplierSalesPrincipal: '',
    supplierSalesPrincipalPhone: '',
    supplierBankName: '',
    supplierBankAccount: '',

    // 合同内容
    contractItems: [],
    contractAttachment: [],
    contractBody: '',

    // 扩展字段（用于表单显示）
    projectName: '',
    sectionName: '',
    purchaserContact: '',
    paymentMethod: '',
    paymentTerm: '',
    isElectronicSignature: false,
    salesContact: '',
    salesPhone: '',
    receivingBank: '',
    receivingAccount: '',
    template: '',
  });

  // 重置其他相关数据
  validityPeriod.value = [];
  renderMaterialList.value = [];
  selectedMaterialIds.value = [];
  deptUserList.value = [];

  // 重置选中的项目和相关选项
  selectedProject.value = null;
  sectionOptions.value = [];
  supplierOptions.value = [];
  allAwardedSuppliers.value = [];
  selectedSection.value = null;
  selectedSupplier.value = null;
  noticeInfo.value = null;

  // 重置模板内容
  templateContent.value = '';

  // 清空表单验证状态
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

const handleSubmit = async () => {
  try {
    if (submitting.value === true) {
      return;
    }

    submitting.value = true;
    // 根据模式调用不同的API
    contractForm.contractItems = renderMaterialList.value.map((item) => ({
      id: item.id,
      contractQuantity: item.awardedQuantity,
      contractAmount: precisionMath.multiply(
        item.paymentList?.find((payment: any) => payment.value === item.procurementProjectPaymentId)?.price ?? 0,
        item.awardedQuantity || 0
      ),
    }));

    contractForm.contractAttachment = contractForm.contractAttachment?.map((item: any) => ({
      ...item,
      filePath: item.url,
      fileName: item.name,
    }));

    contractForm.totalAmount = contractTotalAmount.value;

    // 根据 type 和 mode 决定使用哪个接口
    if (props.type === 'change') {
      // 变更合同使用不同的接口
      const contractSaveReq = {
        changeTypeEnum: 'CHANGE_CONTRACT',
        noticeId: noticeInfo.value?.id,
        projectId: contractForm.projectId,
        contractSaveReq: { ...contractForm },
      };
      if (props.mode === 'create') {
        await addNoticeChange(contractSaveReq);
        resetForm();
      } else if (props.mode === 'edit') {
        await updateNoticeChange(contractSaveReq);
        init();
      }
    } else {
      // 普通合同使用原有接口
      if (props.mode === 'create') {
        await createContract(contractForm);
        resetForm();
      } else if (props.mode === 'edit') {
        await updateContract(contractForm);
        init();
      }
    }
    activeModule.value = 'form';
    emit('success');
  } catch (error) {
  } finally {
    submitting.value = false;
  }
};

const handleCancel = () => {
  emit('cancel');
};

// 处理物料选择变化
const handleMaterialSelectionChange = (selection: any[]) => {
  selectedMaterialIds.value = selection.map((item) => item.id);
};

// 批量删除物料
const handleBatchDeleteMaterials = () => {
  if (selectedMaterialIds.value.length === 0) {
    ElMessage.warning('请先选择要删除的物料');
    return;
  }

  ElMessageBox.confirm('确定要删除选中的物料吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    // 从渲染列表中移除选中的物料
    renderMaterialList.value = renderMaterialList.value.filter((item) => !selectedMaterialIds.value.includes(item.id));
    selectedMaterialIds.value = [];
    ElMessage.success('删除成功');
  });
};

// 打开选择物料模态框
const handleOpenMaterialSelector = () => {
  materialSelectorVisible.value = true;
};

// 确认选择物料
const handleConfirmMaterialSelection = (selectedMaterials: any[]) => {
  // 将选中的物料添加到渲染列表
  selectedMaterials.forEach((material) => {
    if (!renderMaterialList.value.find((item) => item.id === material.id)) {
      renderMaterialList.value.push(material);
    }
  });
  materialSelectorVisible.value = false;
  ElMessage.success('物料添加成功');
};

// 获取可选物料列表（总量数据中不包含渲染列表中的数据）
const getAvailableMaterials = () => {
  const renderIds = renderMaterialList.value.map((item) => item.id);
  return awardDetailList.value.filter((item) => !renderIds.includes(item.id));
};

// 获取中标明细数据
async function getAwardDetailData() {
  if (!contractForm.tenantSupplierId || !contractForm.sectionId || !noticeInfo.value?.id || !selectedProject.value?.id) {
    return;
  }

  try {
    const { data } = await getAwardedItems({
      noticeId: noticeInfo.value?.id,
      projectId: selectedProject.value?.id,
      tenantSupplierId: contractForm.tenantSupplierId,
      sectionId: contractForm.sectionId,
    });

    // 先设置动态列，再处理表格数据
    setDynamicColumn();
    handleTableData(data.srmTenderBidderQuoteItems);

    awardDetailList.value = tableData.value;
    // 初始化渲染列表，默认包含所有数据
    renderMaterialList.value = [...tableData.value];
  } catch (error) {
    awardDetailList.value = [];
    renderMaterialList.value = [];
  } finally {
  }
}

const getConstractDetailAssembler = async () => {
  // 设置有效期
  if (contractForm.effectStartDate && contractForm.effectEndDate) {
    validityPeriod.value = [contractForm.effectStartDate, contractForm.effectEndDate];
  }

  // 设置选中的项目
  if (contractForm.projectId) {
    const project = projectOptions.value.find((p) => p.id === contractForm.projectId?.toString());
    project?.projectCode && getProjectDetailData({ projectCode: project?.projectCode });
    if (project) {
      setSelectedProject(project);
      // 确保projectId是字符串类型，以便与选择框的value匹配
      contractForm.projectId = project.id;

      // 先获取公告信息，再获取中标供应商数据（标段信息从中标供应商中提取）
      await getEffectNoticeData();
      await getAwardedSuppliersData(project.id, noticeInfo.value?.id);
      handlePurchaserDeptChange(contractForm?.purchaserDeptId || '');

      // 设置选中的标段
      if (contractForm.sectionId) {
        const section = sectionOptions.value.find((s) => s.id === contractForm.sectionId?.toString());
        if (section) {
          contractForm.sectionId = section.id;
          contractForm.sectionName = section.sectionName;

          // 重新过滤供应商
          filterSuppliersBySection();

          await awardStore.loadForAwardResult(noticeInfo.value?.id || 0, Number(selectedProject.value?.id) || 0, true);
          // 设置选中的供应商
          if (contractForm.tenantSupplierId) {
            const supplier = supplierOptions.value.find((s) => s.tenantSupplierId === contractForm.tenantSupplierId?.toString());
            if (supplier) {
              contractForm.supplierName = supplier.supplierName;
              contractForm.supplierSalesPrincipal = supplier.contactName || '';
              contractForm.supplierSalesPrincipalPhone = supplier.contactPhone || '';
              await getAwardDetailData(); // 立即获取物料数据
              // 如果有合同清单数据，设置到渲染列表中
              if (contractForm.contractItems && contractForm.contractItems.length > 0) {
                // 将合同清单数据合并到渲染列表中
                renderMaterialList.value = contractForm.contractItems.map((contractItem: any) => {
                  // 从 awardDetailList 中找到对应的物料信息
                  const awardItem = awardDetailList.value.find((item: any) => item.id === contractItem.id?.toString());
                  if (awardItem) {
                    return {
                      ...awardItem,
                      awardedQuantity: contractItem.contractQuantity,
                      bidAmount: contractItem.contractAmount,
                    };
                  }
                  return contractItem;
                });
              }
            }
          }
        }
      }
    }
  }
};

// 初始化
const init = async () => {
  try {
    submitting.value = true;
    // 获取项目列表
    await getProjectList({ projectName: props?.projectName ?? '' });
    // 获取模板列表
    const response = await getTemplateList({ type: 'CONTRACT' });
    if (response.code === 0) {
      templateOptions.value = response.data?.records;
      // 默认选中第一个模版
      if (templateOptions.value && templateOptions.value.length > 0) {
        contractForm.template = templateOptions.value[0].id;
        // 自动加载第一个模版的内容
        await handleTemplateChange();
      }
    }

    if (!props.type && props.mode !== 'create' && props.contractId) {
      let response;
      response = await getContractDetail(props.contractId, isPurchaser.value);
      if (
        ['APPROVING', 'APPROVE_REJECT'].includes(response.data?.approvalStatus) &&
        response.data?.contractStatus === 'SUPPLIER_REJECTED' &&
        !isPurchaser.value
      ) {
        const result = await getContractOldDetail(props.contractId);
        result.data = JSON.parse(result.data ?? '{}');
        response = result;
      }

      if (response.code === 0) {
        Object.assign(contractForm, response.data);
      }
      await getConstractDetailAssembler();
    } else if (props.type === 'change' && props.mode === 'create') {
      const response = await getContractDetail(props.contractId, isPurchaser.value);
      if (response.code === 0) {
        Object.assign(contractForm, response.data);
      }
      await getConstractDetailAssembler();
    } else if (props.type === 'change' && props.mode !== 'create') {
      const response = await getNoticeChangeDetailInfo({ bizId: props.contractId, changeTypeEnum: 'CHANGE_CONTRACT' });
      if (response.code === 0) {
        // 从 contractSaveReq 中取数据
        Object.assign(contractForm, response.data?.contractSaveReq || {});
      }
      await getConstractDetailAssembler();
    }

    submitting.value = false;
  } catch (error) {
    console.log(error, 'error');
  }
};

// 格式化有效期显示
const formatValidityPeriod = () => {
  if (contractForm.effectStartDate && contractForm.effectEndDate) {
    return `${contractForm.effectStartDate} 至 ${contractForm.effectEndDate}`;
  }
  return '--';
};

// 获取模板名称
const getTemplateName = (templateId: string | number | undefined) => {
  if (!templateId) return '--';
  const template = templateOptions.value.find((t) => t.id === templateId);
  return template ? template.templateName : '--';
};

// 监听合同总金额变化，更新表单数据
watch(contractTotalAmount, (newAmount) => {
  contractForm.totalAmount = newAmount;
});

onMounted(() => {
  init();
  // 初始化部门树数据
  getDeptTree();
});

onActivated(() => {
  if (contractStore.getAndClearResetForm() && props.mode === 'create') {
    resetForm();
  }
});

// 暴露 init 方法给父组件
defineExpose({
  init,
});
</script>

<style lang="scss" scoped>
.contract-form-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f7fa;
  padding: 20px;
  gap: 20px;
}

:deep(.el-cascader) {
  width: 100%;
}

.module-switch {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.form-module {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
}

.form-section {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  margin-bottom: 20px;
}

.header-title {
  color: var(--Color-Text-text-color-primary, #1d2129);
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
  padding-left: 10px;
  position: relative;

  &::before {
    content: '';
    display: inline-block;
    width: 2px;
    height: 14px;
    background: var(--Color-Primary-color-primary, #0069ff);
    margin-right: 8px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}

.section-content {
  padding-left: 10px;
}

.material-description {
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  color: #f56c6c;
  font-size: 14px;
  padding: 12px;
  margin-bottom: 16px;
}

.material-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.material-table {
  :deep(.el-table__header) {
    th {
      background: var(--Color-Fill-fill-color-light, #f5f7fa);
      padding: 6px 0;
      border-bottom: 1px solid var(--Color-Border-border-color-light, #ebeef5);

      .cell {
        color: var(--Color-Text-text-color-regular, #505762);
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
      }
    }
  }

  :deep(.el-table__body) {
    tr {
      &:hover {
        background-color: #fff !important;

        td {
          background-color: #fff !important;
        }
      }

      td {
        border-bottom: 1px solid #ebeef5;
        padding: 6px 0;
      }
    }
  }
}

.upload-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.upload-tip {
  color: #909399;
  font-size: 12px;
  padding: 12px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  text-align: center;
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;

  .file-name {
    color: var(--Color-Primary-color-primary, #0069ff);
    font-size: 14px;
    cursor: pointer;
  }
}

.content-module {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: hidden;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.template-selector {
  display: flex;
  align-items: center;
  gap: 12px;

  .template-tip {
    color: #909399;
    font-size: 12px;
  }
}

.content-actions {
  display: flex;
  gap: 8px;
}

.content-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.contract-content {
  width: 88%;
  min-height: 100%;
  margin: 0 auto;
  background: #fff;
  padding: 8px;
  border-radius: 2px;
  box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.1);
}

.content-preview {
  width: 88%;
  min-height: 100%;
  margin: 0 auto;
  background: #fff;
  padding: 8px;
  border-radius: 2px;
}

.form-actions-wrapper {
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  z-index: 1000;
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 24px;
  display: flex;
  border-top: 1px solid var(--Color-Border-border-color-light, #e4e7ed);

  .form-actions {
    display: flex;
    justify-content: flex-start;
    gap: 12px;
    width: 100%;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .contract-form-container {
    padding: 16px;
  }

  .form-module {
    padding: 16px;
  }

  .content-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .template-selector {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .content-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .contract-content {
    padding: 20px;
  }

  .form-actions {
    flex-wrap: wrap;
    gap: 8px;
  }

  // 查看模式样式
  :deep(.el-descriptions) {
    .el-descriptions__header {
      margin-bottom: 16px;
    }

    .el-descriptions__body {
      .el-descriptions__table {
        .el-descriptions__cell {
          padding: 12px 16px;

          &.el-descriptions__label {
            background-color: #fafafa;
            font-weight: 500;
            color: #606266;
          }

          &.el-descriptions__content {
            color: #303133;
          }
        }
      }
    }
  }

  .attachment-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;

    .file-name {
      color: #409eff;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// 审批设置样式
.approval-wrapper {
  display: flex;
  gap: 12px;
}

.approval-radio-group {
  display: flex;
  gap: 16px;
}

.user-selector-wrapper {
  margin-top: 8px;
}

@media (max-width: 768px) {
  .approval-radio-group {
    flex-direction: column;
    gap: 8px;
  }

  .approval-wrapper {
    gap: 8px;
  }
}
</style>
