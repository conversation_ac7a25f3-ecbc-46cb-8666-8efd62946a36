<template>
  <div class="change-announcement-container">
    <el-form
      ref="formRef"
      :model="localForm"
      :rules="formRules"
      label-width="140px"
    >
      <!-- 招标要求 -->
      <el-row :gutter="24">
        <template v-if="['JZTP', 'ZB'].includes(props.sourcingType)">
          <el-col :span="12">
            <el-form-item
              label="开标方式"
              prop="tenderWay"
            >
              <el-radio-group v-model="localForm.tenderWay">
                <el-radio label="ONLINE">线上开标</el-radio>
                <el-radio label="OFFLINE">线下开标</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="开标地点"
              prop="bidOpeningAddress"
            >
              <el-input
                v-model="localForm.bidOpeningAddress"
                placeholder="请输入开标地点"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="文件递交地址"
              prop="fileSubmissionAddress"
            >
              <el-input
                v-model="localForm.fileSubmissionAddress"
                placeholder="请输入文件递交地址"
              />
            </el-form-item>
          </el-col>
        </template>
        <el-col :span="12">
          <el-form-item label="项目所在地区">
            <el-cascader
              v-model="selectedAreaPath"
              style="width: 100%"
              clearable
              showAllLevels
              :options="addressOptions"
              :props="{
                multiple: false,
                value: 'value',
                label: 'label',
                children: 'children',
                checkStrictly: false,
                emitPath: true,
                expandTrigger: 'hover',
              }"
              @change="handleAreaChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="项目详细地址"
            prop="address"
          >
            <el-input
              v-model="localForm.address"
              placeholder="请输入详细地址"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item
        label="报价是否含税"
        required
      >
        <el-radio-group v-model="localForm.includeTax">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="发票要求">
        <el-radio-group v-model="localForm.certificateType">
          <el-radio label="SPECIAL">专票</el-radio>
          <el-radio label="NORMAL">普票</el-radio>
          <el-radio label="NONE">无要求</el-radio>
        </el-radio-group>
      </el-form-item>

      <div class="section-split-line"></div>

      <el-form-item label="评审规则">
        <el-radio-group v-model="localForm.evaluationMethod">
          <el-radio label="LOWEST_PRICE">最低价法</el-radio>
          <el-radio label="COMPREHENSIVE">经评审最低价法</el-radio>
          <el-radio label="COMPREHENSIVE_SCORE">综合评价法</el-radio>
          <el-radio label="HIGHEST_PRICE">最高价法</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item
        label="报价须知"
        prop="quotationNotice"
      >
        <el-input
          v-model="localForm.quotationNotice"
          type="textarea"
          :rows="3"
          placeholder="请输入报价须知"
        />
      </el-form-item>

      <!-- 联系方式 -->
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item
            label="采购联系人"
            prop="contactPerson"
            required
          >
            <el-input
              v-model="localForm.contactPerson"
              placeholder="请输入采购联系人"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="联系电话"
            prop="contactPhone"
          >
            <el-input
              v-model="localForm.contactPhone"
              placeholder="请输入联系电话"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item
            label="固定电话"
            prop="contactFixedPhone"
          >
            <el-input
              v-model="localForm.contactFixedPhone"
              placeholder="请输入固定电话"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="电子邮件"
            prop="contactEmail"
          >
            <el-input
              v-model="localForm.contactEmail"
              placeholder="请输入电子邮件"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <div class="section-split-line"></div>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item
            label="变更公告标题"
            prop="announcementTitle"
            required
          >
            <el-input
              v-model="localForm.announcementTitle"
              placeholder="请输入变更公告标题"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item
            label="发布媒体"
            prop="checkboxGroup1"
          >
            <el-checkbox-group
              v-model="localForm.checkboxGroup1"
              size="small"
            >
              <el-checkbox
                v-for="item in localForm.publishMedia"
                :key="item.mediaName"
                :label="item.mediaName"
                size="large"
                border
              >
                {{ item.mediaName }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="变更公告附件">
            <YunUpload
              v-model="attachmentInfos"
              @change="handleUploadFile"
            ></YunUpload>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 审批设置 -->
      <el-row :gutter="24">
        <el-col :span="14">
          <el-form-item
            label="发起审批"
            prop="specialProcessExecutorList"
          >
            <div class="flex items-center gap-2">
              <el-radio-group
                v-model="localForm.approvalType"
                @change="handleApprovalTypeChange"
              >
                <el-radio :label="0">系统自动发起</el-radio>
                <el-radio :label="1">指定审批人</el-radio>
              </el-radio-group>
              <UserSelector
                v-model="localForm.specialProcessExecutorList"
                v-show="localForm.approvalType === 1"
                :multiple="true"
                style="width: auto !important"
                placeholder="请选择审批人"
                @update:model-value="handleApprovalDataChange"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 公告详情编辑抽屉 -->
    <AnnouncementEditor
      ref="editorRef"
      :title1="'变更公告'"
      :readonly="isViewMode"
      @save="handleEditorSave"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue';
import AnnouncementEditor from '@/views/procurementSourcing/biddingProcess/components/award/AwardResult/components/AnnouncementEditor.vue';
import YunUpload from '@/components/YunUpload/index.vue';
import UserSelector from '@/components/UserSelector/index.vue';
import { Session } from '@/utils/storage';
import { getNoticeChangeDetailInfo } from '@/api/purchasing/noticeChange';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { getAnnouncementData} from '@/views/procurementSourcing/biddingProcess/api';

const biddingStore = useBiddingStore();

const noticeId = computed(() => biddingStore?.noticeId);

interface AttachmentInfo {
  fileName: string;
  filePath: string;
}

interface Props {
  formData: any;
  isViewMode?: boolean;
  projectInfo?: any;
  sourcingType?: any;
}

const props = withDefaults(defineProps<Props>(), {
  isViewMode: false,
});

const emit = defineEmits<{
  (e: 'update:form-data', data: any): void;
  (e: 'save-data', data: any): void;
}>();

// 表单引用
const formRef = ref();
const editorRef = ref();

// 地区选择相关
const selectedAreaPath = ref<string[]>([]);
const addressOptions = ref([]);

// 附件上传相关
const attachmentInfos = ref<{ name: string; url: string }[]>([]);

// 加载状态
const loading = ref(false);

// 处理审批类型变化
const handleApprovalTypeChange = (value: number) => {
  localForm.approvalType = value;
  if (value === 0) {
    localForm.specialProcessExecutorList = [];
  }
};

// 处理审批数据变化
const handleApprovalDataChange = (value: string[] | string) => {
  if (Array.isArray(value)) {
    localForm.specialProcessExecutorList = value;
  } else {
    localForm.specialProcessExecutorList = value ? [value] : [];
  }
};

// 本地表单数据
const localForm = reactive({
  // 竞谈/招标增加
  tenderWay: '',
  bidOpeningAddress: '',
  fileSubmissionAddress: '',
  // 地区信息
  province: '',
  city: '',
  district: '',
  address: '',
  // 基础信息
  includeTax: '1',
  certificateType: 'NONE',
  evaluationMethod: 'LOWEST_PRICE',
  quotationNotice: '',
  contactPerson: '',
  contactPhone: '',
  contactFixedPhone: '',
  contactEmail: '',
  announcementTitle: '',
  announcementContent: '',
  checkboxGroup1: [],
  // 发布媒体
  publishMedia: [
    { mediaName: '中国采购与招标网', selected: false },
    { mediaName: '中国招标投标公共服务平台', selected: false },
  ],
  // 附件信息
  attachmentInfos: [] as AttachmentInfo[],
  // 审批设置
  approvalType: 0,
  specialProcessExecutorList: [] as string[],
});

// 表单验证规则
const formRules = {
  tenderWay: [{ required: true, message: '请选择开标方式', trigger: 'change' }],
  bidOpeningAddress: [{ required: true, message: '请输入开标地点', trigger: 'blur' }],
  fileSubmissionAddress: [{ required: true, message: '请输入文件递交地址', trigger: 'blur' }],
  address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
  contactPerson: [{ required: true, message: '请输入采购联系人', trigger: 'blur' }],
  announcementTitle: [{ required: true, message: '请输入变更公告标题', trigger: 'blur' }],
  specialProcessExecutorList: [
    {
      required: false,
      validator: (rule: any, value: any, callback: any) => {
        if (localForm.approvalType === 1 && (!localForm.specialProcessExecutorList || localForm.specialProcessExecutorList.length === 0)) {
          callback(new Error('请选择审批人'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ],
};

// 获取地址选项数据
function fetchAddressOptions() {
  const requestUrl = 'https://oss-public.yunlizhi.cn/frontend/yun-design/area.json';
  const token = Session.getToken();
  const tenantId = Session.getTenant();

  const requestConfig = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'TENANT-ID': tenantId,
      AUTHORIZATION: `Bearer ${token}`,
    },
  };

  fetch(requestUrl, requestConfig)
    .then((response) => {
      if (!response.ok) {
        throw new Error('API request failed');
      }
      return response.json();
    })
    .then((data) => {
      const processData = (items: any[]) => {
        return items.map((item: any) => ({
          label: item['areaName'],
          value: item['areaCode'],
          children: item['children'] ? processData(item['children']) : undefined,
        }));
      };

      addressOptions.value = Array.isArray(data) ? processData(data) : [];
    })
    .catch((error) => {
      addressOptions.value = [];
    });
}

// 处理地区选择变化
const handleAreaChange = (value: any) => {
  if (value && value.length >= 3) {
    // 根据选中的路径，从addressOptions中找到对应的省市区名称
    const findAreaNames = (options: any[], codes: string[], level: number = 0): string[] => {
      if (level >= codes.length || level >= 3) return [];

      const currentCode = codes[level];
      const currentOption = options.find((option) => option.value === currentCode);

      if (!currentOption) return [];

      const result = [currentOption.label];

      if (level < 2 && currentOption.children && codes[level + 1]) {
        const childNames = findAreaNames(currentOption.children, codes, level + 1);
        result.push(...childNames);
      }

      return result;
    };

    const areaNames = findAreaNames(addressOptions.value, value);

    if (areaNames.length >= 3) {
      localForm.province = areaNames[0];
      localForm.city = areaNames[1];
      localForm.district = areaNames[2];
    }
  } else {
    // 清空选择
    localForm.province = '';
    localForm.city = '';
    localForm.district = '';
  }
};

// 处理附件上传
const handleUploadFile = (file: any) => {
  localForm.attachmentInfos = attachmentInfos.value.map((item) => {
    return {
      fileName: item.name,
      filePath: item.url,
    };
  });
};

// 封装数据回填函数
const fillFormData = (detailData: any) => {
  if (!detailData) return;

      // 填充表单数据
      if (detailData.quotationDemand) {
        localForm.province = detailData.quotationDemand.province || '';
        localForm.city = detailData.quotationDemand.city || '';
        localForm.district = detailData.quotationDemand.district || '';
        localForm.address = detailData.quotationDemand.address || '';
        localForm.includeTax = detailData.quotationDemand.includeTax?.toString() || '1';
        localForm.certificateType = detailData.quotationDemand.certificateType || 'NONE';

    // 设置地区级联选择器的值
    if (localForm.province && localForm.city && localForm.district) {
      // 根据省市区名称找到对应的编码
      const findAreaPath = (options: any[], targetNames: string[]): string[] => {
        const path: string[] = [];
        let currentOptions = options;

        for (let i = 0; i < targetNames.length && i < 3; i++) {
          const targetName = targetNames[i];
          const found = currentOptions.find((option) => option.label === targetName);

          if (found) {
            path.push(found.value);
            currentOptions = found.children || [];
          } else {
            break;
          }
        }

        return path;
      };

      const areaPath = findAreaPath(addressOptions.value, [localForm.province, localForm.city, localForm.district]);

      if (areaPath.length === 3) {
        selectedAreaPath.value = areaPath;
      }
    }
  }

  // 填充基础信息
  localForm.evaluationMethod = detailData.evaluationMethod || 'LOWEST_PRICE';
  localForm.quotationNotice = detailData.quotationNotice || '';
  localForm.announcementTitle = detailData.noticeTitle || '';
  localForm.announcementContent = detailData.noticeContent || '';

  // 竞谈、招标新增(tenderWay、bidOpeningAddress、fileSubmissionAddress)
  localForm.tenderWay = detailData.tenderWay || '';
  localForm.bidOpeningAddress = detailData.bidOpeningAddress || '';
  localForm.fileSubmissionAddress = detailData.purchaseDateDemand?.fileSubmissionAddress || '';

  // 填充联系方式
  if (detailData.contactInfo) {
    localForm.contactPerson = detailData.contactInfo.contactPerson || '';
    localForm.contactPhone = detailData.contactInfo.contactPhone || '';
    localForm.contactFixedPhone = detailData.contactInfo.contactFixedPhone || '';
    localForm.contactEmail = detailData.contactInfo.contactEmail || '';
  }

  // 填充发布媒体
  if (detailData.publishMedia && Array.isArray(detailData.publishMedia)) {
    localForm.publishMedia = detailData.publishMedia;
    localForm.checkboxGroup1 = detailData.publishMedia.filter((media: any) => media.selected).map((media: any) => media.mediaName);
  }

  // 填充附件信息
  if (detailData.attachmentInfos && Array.isArray(detailData.attachmentInfos)) {
    localForm.attachmentInfos = detailData.attachmentInfos;
    attachmentInfos.value = detailData.attachmentInfos.map((item: any) => ({
      name: item.fileName,
      url: item.filePath,
    }));
  }

  // 填充审批数据
  localForm.approvalType = detailData.approvalType || 0;
  localForm.specialProcessExecutorList = detailData.specialProcessExecutorList || [];
};

// 获取详情数据
const fetchDetailData = async () => {
  if (!props.formData || !props.formData.noticeId) {
    return;
  }

  try {
    loading.value = true;

    const params = {
      noticeId: props.formData.noticeId,
      projectId: props.formData.projectId || 0,
      changeTypeEnum: props.formData.changeType || '',
    };

    const response = await getNoticeChangeDetailInfo(params);

    if (response && response.data) {
      const detailData = response.data;
      // 使用封装的函数回填数据
      fillFormData(detailData);
    }
  } catch (error) {
    console.error('获取详情数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 监听父组件数据变化
watch(
  () => props.formData,
  (newVal) => {
    if (newVal) {
      Object.assign(localForm, newVal);
    }
  },
  { immediate: true, deep: true }
);

// 编辑器保存回调
const handleEditorSave = (content: string) => {
  localForm.announcementContent = content;

  const projectId = props.projectInfo?.id || props.projectInfo?.projectId || '';
  // 调用转换方法
  const apiData = convertToApiData(projectId);
  emit('submit-success', apiData);
  // 同时更新表单数据
  emit('update:form-data', apiData);
};

// 打开编辑器
const openEditor = () => {
  if (editorRef.value) {
    editorRef.value.show(localForm.announcementContent, localForm.announcementTitle || '编辑变更公告内容');
  }
};

// 表单验证
const validate = async () => {
  try {
    await formRef.value?.validate();
    openEditor();
    const projectId = props.projectInfo?.id || props.projectInfo?.projectId || '';
    // 调用转换方法
    const apiData = convertToApiData(projectId);

    // 通过 emit 将转换后的数据传递给父组件
    // emit('update:form-data', apiData);
    return false;
  } catch (error: any) {
    throw error;
  }
};

// 将当前组件数据转换为与 ProcurementDocument 保存时相同的数据结构
const convertToApiData = (projectId: string, templateId?: string) => {
  return {
    // 基础信息
    noticeTitle: localForm.announcementTitle,
    noticeTemplateId: templateId || '', // 变更公告可能没有模板ID
    projectId: projectId,
    noticeContent: localForm.announcementContent,
    // 竞谈、招标新增（tenderWay、bidOpeningAddress）
    tenderWay: localForm.tenderWay,
    bidOpeningAddress: localForm.bidOpeningAddress,

    // 采购时间要求 - 变更公告可能不需要，提供默认值
    purchaseDateDemand: {
      registerStartTime: '',
      registerEndTime: '',
      auditStartTime: '',
      auditEndTime: '',
      quoteStartTime: '',
      quoteEndTime: '',
      bidOpenTime: '',
      bidOpener: '',
      fileSubmissionAddress: localForm.fileSubmissionAddress, // 竞谈、招标新增
    },

    // 报价要求 - 整合地区信息和其他信息
    quotationDemand: {
      province: localForm.province,
      city: localForm.city,
      district: localForm.district,
      address: localForm.address,
      includeTax: parseInt(localForm.includeTax), // 字符串转数字
      certificateType: localForm.certificateType,
    },

    // 标段要求 - 变更公告通常没有标段信息，提供空数组
    bidsSegments: [],

    // 评审规则
    evaluationMethod: localForm.evaluationMethod,

    // 供应商报价须知
    quotationNotice: localForm.quotationNotice,

    // 联系方式 - 整合为对象结构
    contactInfo: {
      contactPerson: localForm.contactPerson,
      contactPhone: localForm.contactPhone,
      contactFixedPhone: localForm.contactFixedPhone,
      contactEmail: localForm.contactEmail,
    },

    // 发布媒体 - 处理选中状态
    publishMedia: localForm.publishMedia.map((media) => ({
      ...media,
      selected: localForm.checkboxGroup1.includes(media.mediaName),
    })),

    // 附件信息
    attachmentInfos: localForm.attachmentInfos || [],
    // 审批设置
    approvalType: localForm.approvalType,
    specialProcessExecutorList: localForm.specialProcessExecutorList,
  };
};

// 初始化
onMounted(async () => {
  fetchAddressOptions();

  // 等待地址选项加载完成
  await new Promise((resolve) => setTimeout(resolve, 100));

  // 如果是查看模式，获取详情数据
  if (props.isViewMode) {
    await fetchDetailData();
  } else {
    // 非查看模式，获取公告详情数据
    if(!noticeId.value) { return; }
    const { data } = await getAnnouncementData(noticeId.value);

    // 使用封装的函数回填数据
    if (data) {
      fillFormData(data);
    }
  }
});

// 导出验证方法
defineExpose({
  validate,
  openEditor,
  convertToApiData,
  fetchDetailData,
});
</script>

<style scoped lang="scss">
.section-split-line {
  background: #dcdfe6;
  height: 1px;
  margin-bottom: 24px;
}

.upload-section {
  display: flex;
  align-items: center;
  gap: 12px;

  span {
    font-size: 14px;
    color: #606266;
  }
}

.form-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 24px;
  border-top: 1px solid #e4e7ed;
  margin-top: 24px;
}
</style>
