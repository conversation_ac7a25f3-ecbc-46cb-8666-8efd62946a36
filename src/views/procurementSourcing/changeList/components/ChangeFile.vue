<template>
  <div class="change-file-container">
    <!-- 复用竞谈文件组件 -->
    <CNDocument
      ref="cnDocumentRef"
      :hideAction="true"
      :mode="props.isViewMode ? 'change' : ''"
      :hideAnchor="true"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, defineExpose } from 'vue';
import CNDocument from '@/views/procurementSourcing/biddingProcess/components/announcement/CNDocument/index.vue'

interface Props {
  formData: any;
  isViewMode?: boolean;
  projectInfo?: any;
  sourcingType?: any;
}

const props = withDefaults(defineProps<Props>(), {
  isViewMode: false,
});

// 组件引用
const cnDocumentRef = ref();

defineExpose({
  cnDocumentRef
});
</script>