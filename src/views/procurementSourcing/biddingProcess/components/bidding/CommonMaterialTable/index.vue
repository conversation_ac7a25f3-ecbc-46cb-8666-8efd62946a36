<template>
  <div
    class="document-review-container"
    :class="{ 'is-hide-stage-tabs': !isShowSection }"
    v-loading="pageLoading"
  >
    <template v-if="![SCENE_ENUMS.XJCG_TB, SCENE_ENUMS.JZTP_ZB_KB]?.includes(scene)">
      <StageTabs
        v-model="activeTabIndex"
        @update:sectionId="handleSectionChange"
      />
    </template>

    <div
      class="other-info-wrapper"
      v-if="[SCENE_ENUMS.ZJWT_TB, SCENE_ENUMS.ZJWT_KB]?.includes(scene)"
    >
      <!-- 左侧信息组 -->
      <div class="info-left">
        <template v-if="scene === SCENE_ENUMS.ZJWT_TB">
          <el-tooltip
            class="item"
            effect="dark"
            placement="top"
            :content="`报价供应商：${extraInfo.supplierName || '--'}`"
          >
            <div class="item-label">报价供应商：{{ extraInfo.supplierName || '--' }}</div>
          </el-tooltip>
          <div class="item-block">
            <span class="label">报价时间:</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              :content="extraInfo?.quoteTime ? moment(extraInfo?.quoteTime).format('YYYY-MM-DD HH:mm:ss') : '--'"
            >
              <span class="value ellipsis-value">{{ extraInfo?.quoteTime ? moment(extraInfo?.quoteTime).format('YYYY-MM-DD HH:mm:ss') : '--' }}</span>
            </el-tooltip>
          </div>
          <div class="item-block">
            <span class="label">报价IP:</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              :content="extraInfo?.quoteIp || '--'"
            >
              <span class="value ellipsis-value">{{ extraInfo?.quoteIp || '--' }}</span>
            </el-tooltip>
          </div>
        </template>
      </div>

      <!-- 右侧附件组 -->
      <div
        class="info-right"
        v-if="[SCENE_ENUMS.ZJWT_TB, SCENE_ENUMS.ZJWT_KB]?.includes(scene)"
      >
        <div class="item-block file-item">
          <span class="label">报价文件:</span>
          <template v-if="extraInfo?.quoteAttachments">
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              :content="extraInfo?.quoteAttachments?.fileName"
            >
              <span class="value file-value">{{ extraInfo?.quoteAttachments?.fileName || '--' }}</span>
            </el-tooltip>
            <span
              class="download-btn"
              v-if="extraInfo?.quoteAttachments?.filePath"
            >
              <el-link
                :href="extraInfo?.quoteAttachments?.filePath"
                target="_blank"
                type="primary"
              >
                下载
              </el-link>
            </span>
          </template>
          <span
            class="value"
            v-else
            >--</span
          >
        </div>
        <div class="item-block file-item">
          <span class="label">其他附件:</span>
          <template v-if="extraInfo?.otherAttachments">
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              :content="extraInfo?.otherAttachments.fileName"
            >
              <span class="value file-value">{{ extraInfo?.otherAttachments.fileName || '--' }}</span>
            </el-tooltip>
            <span
              class="download-btn"
              v-if="extraInfo?.otherAttachments.filePath"
            >
              <el-link
                :href="extraInfo?.otherAttachments.filePath"
                target="_blank"
                type="primary"
              >
                下载
              </el-link>
            </span>
          </template>
          <span
            class="value"
            v-else
            >--</span
          >
        </div>
      </div>
    </div>

    <div
      v-if="!pageLoading"
      class="flex-1 need-hide-table-card editable-table table-container"
    >
      <el-button
        class="right-button xjcg-export"
        v-if="searchFields.length"
        @click="handleDownloadMaterialTemplate"
      >
        数据导出
      </el-button>
      <yun-pro-table
        ref="proTableRef"
        v-model:pagination="pagination"
        v-model:filter-data="filterTableData"
        v-model:searchData="searchData"
        :table-columns="dynamicColumn"
        :search-fields="searchFields"
        :auto-height="autoHeight"
        :remote-method="remoteMethod"
        :table-props="tablePropsObj"
        :search-props="searchPropsObj"
        layout="whole"
        :default-fetch="false"
        :extra="{
          position: 'breaster',
        }"
      >
        <template #extra>
            <div
              class="alert-section"
              v-if="quoteLessThanThreeMaterialCount && [SCENE_ENUMS.XJCG_TB]?.includes(scene)"
            >
              <img
                src="https://oss-public.yunlizhi.cn/frontend/fe-procurement-platform/alert_icon.svg"
                alt=""
              />
              <div class="flex items-center">
                <span>提醒：{{ quoteLessThanThreeMaterialCount }}条物料报价供应商不足3家！</span>
                <span
                  class="cursor-pointer"
                  @click="getQuoteLessThanThreeMaterialCode"
                  >请查看</span
                >
              </div>
            </div>
            <div
              v-if="tableData?.length && showIpAlert && handleShowIpAlert?.length && ![SCENE_ENUMS.ZJWT_KB, SCENE_ENUMS.ZJWT_TB]?.includes(scene)"
              class="flex justify-end"
            >
              <div class="common-ip-alert">
                <img
                  class="alert-icon"
                  src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform/WarnTriangleFilled.svg"
                  alt=""
                />
                <span class="alert-msg">系统监测到 {{ handleShowSupplierAlert?.length }} 家供应商IP异常</span>
                <img
                  class="alert-close"
                  src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform/close.svg"
                  alt=""
                  @click="handleCloseIpAlert"
                />
              </div>
            </div>
        </template>
        <template
          #t_tableHeaderLeft
          v-if="![SCENE_ENUMS.XJCG_TB]?.includes(scene)"
        ></template>
        <template
          #t_tableHeaderRight
          v-if="![SCENE_ENUMS.XJCG_TB, SCENE_ENUMS.JZTP_ZB_KB]?.includes(scene)"
        >
          <div class="right-section">
            已发起 <span class="highlight">{{ hasQuotation }}</span> 轮报价
          </div>
        </template>
      </yun-pro-table>
    </div>
    <LessThanThreeMaterial ref="lessThanThreeMaterialRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeMount } from 'vue';
// @ts-ignore
import StageTabs from '@/views/procurementSourcing/biddingProcess/components/bidding/StageTabs.vue';
import { useProTable } from '@ylz-use/core';
import { useColumns } from './hooks/useColumns';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { queryQuoteListByMaterial } from '@/api/purchasing/bid';
// import { queryQuoteDetailByMaterial } from '@/api/purchasing/evaluation';
import { ElMessage } from 'yun-design';
import moment from 'moment';
// @ts-ignore
import { useDynamicTable } from '@/views/procurementSourcing/biddingProcess/hooks/useDynamicTable.jsx';
// @ts-ignore
import { useExcel } from '@/views/procurementSourcing/biddingProcess/hooks/useExcel.jsx';
// @ts-ignore
import { SCENE_ENUMS } from '@/views/procurementSourcing/biddingProcess/constants';
// @ts-ignore
import LessThanThreeMaterial from './components/LessThanThreeMaterial/index.vue';

const props = withDefaults(defineProps<{ scene?: any, autoHeight?: boolean }>(), {
  scene: '',
  autoHeight: false,
});
// eslint-disable-next-line no-console
console.log('props', props.scene);
const pageLoading = ref(true);

const biddingStore = useBiddingStore();
const { exportToExcel } = useExcel();
const { dynamicColumn: dynamicColumnOrigin, tableData, setDynamicColumn, handleTableData, objectSpanMethod } = useDynamicTable();
const dynamicColumn = computed(() => {
  let result = dynamicColumnOrigin.value;
  if ([SCENE_ENUMS.ZJWT_TB, SCENE_ENUMS.ZJWT_KB]?.includes(props.scene)) {
    result = dynamicColumnOrigin.value?.filter((item: any) => item.prop !== 'supplierName');
  }
  return [
    ...result,
    {
      prop: 'quoteIp',
      label: '投标IP',
      fieldName: '投标IP',
      fieldCode: 'quoteIp',
      width: 140,
    },
  ];
});

const currentSectionId = ref('');
const activeTabIndex = ref(0);

const noticeInfo = computed(() => biddingStore?.noticeInfo);
const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);
const isZJWT = computed(() => biddingStore?.isZJWT);
// const isXJCG = computed(() => biddingStore?.isXJCG);
const isShowSection = computed(() => biddingStore?.isShowSection);

// const isHideStage = computed(() => {
//   return isZJWT.value || isShowSection.value;
// });

const hasQuotation = computed(() => {
  const list = noticeInfo?.value?.quoteRoundVoList || [];
  const findRound = list.find((item: any) => String(item?.sectionId) === String(currentSectionId.value)) || {};
  return findRound?.currentQuoteRound || 1;
});

const extraInfo = computed(() => {
  const res: any = tableData?.value?.[0] || {};
  const { supplierName, attachmentList, quoteTime, quoteIp } = res;
  const otherAttachments = attachmentList?.find((item: any) => item.businessType === 'QUOTE_OTHER_ATTACHMENT') || null;
  const quoteAttachments = attachmentList?.find((item: any) => item.businessType === 'QUOTE_ATTACHMENT') || null;
  return {
    supplierName,
    quoteTime,
    quoteIp,
    otherAttachments,
    quoteAttachments,
  };
});

// const pagination = ref({});
const { columns, searchFields, searchData, isNewRound }: any = useColumns({ sectionId: currentSectionId, scene: props.scene });

const { remoteMethod, tableProps, proTableRef, filterTableData, reLoad, pagination } = useProTable({
  apiFn: queryQuoteListByMaterial,
  async responseHandler(result: any) {
    const list: any = result?.data?.records || [];
    await setDynamicColumn();
    await handleTableData(list, isNewRound.value);
    return tableData.value || [];
  },
  // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
  customTotalHandler(data: any) {
    return 0;
    // return data?.data?.total || data?.total;
  },
  paramsHandler(params: any) {
    const param = {
      sectionId: currentSectionId.value,
      noticeId: noticeId.value,
      projectId: projectId.value,
      ...params,
    };
    return param;
  },
  // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
  querysHandler(querys: any) {
    const querysData = {
      // ...querys,
      current: pagination.value.page,
      size: 1000, // 先查询所有数据
      // size: pagination.value.size || pagination.value.pageSize,
    };
    return querysData;
  },
  plugins: {
    config: {
      columns: columns.value,
      searchFields: searchFields.value,
    },
    list: ['SEARCH_PLUS'],
  },
});

// 条物料报价供应商不足3家
const quoteLessThanThreeMaterialCount = computed(() => {
  const projectItemIds = [...new Set(tableData.value?.map((item: any) => item?.projectItemId) || [])];
  let count = 0;
  projectItemIds?.forEach((item: any) => {
    const quoteList = tableData.value?.filter((i: any) => i?.projectItemId === item);
    if (quoteList?.length < 3) {
      count++;
    }
  });
  return count;
});

const showIpAlert = ref(true);
const handleCloseIpAlert = () => {
  showIpAlert.value = false;
};
// tableData 数组中存在 quoteIp  tenantSupplierId
// 将 quoteIp 相同的 单元格样式标红 表头样式标红
const handleShowIpAlert = computed(() => {
  const quoteIpList = [...new Set(tableData.value?.map((item: any) => item?.quoteIp))];
  return quoteIpList?.filter((item: any) => {
    return tableData.value?.filter((i: any) => i?.quoteIp === item).length > 1;
  });
});

const handleShowSupplierAlert = computed(() => {
  return [
    ...new Set(tableData.value?.filter((item: any) => handleShowIpAlert.value?.includes(item?.quoteIp))?.map((item: any) => item?.tenantSupplierId)),
  ];
});

const calcCellClassName = ({ row, column }: any) => {
  if (!isZJWT?.value && column?.property === 'quoteIp' && handleShowIpAlert?.value?.includes(row?.quoteIp)) {
    return 'quoteIp-cell-class-name';
  }
};
const calcHeaderCellClassName = ({ column }: any) => {
  if (!isZJWT?.value && column?.property === 'quoteIp' && handleShowIpAlert?.value?.length) {
    return 'quoteIp-header-cell-class-name';
  }
};
// const calcHeaderCellStyle = ({ column }: any) => {
//   if (column?.property === 'quoteIp' && handleShowIpAlert?.value?.length) {
//     return {
//       // color: 'var(--Color-Error-color-error, #FF3B30)',
//     };
//   }
// };
// const calcCellStyle = ({ row, column }: any) => {
//   if (column?.property === 'quoteIp' && handleShowIpAlert?.value?.includes(row?.quoteIp)) {
//     return {
//       // borderRight: '1px solid var(--Color-Border-border-color-lighter, #EBEEF5)',
//       // backgroundColor: 'rgba(255, 59, 48, 0.06)',
//       // color: 'var(---el-color-danger, #F53F3F)',
//     };
//   }
// };

const lessThanThreeMaterialRef = ref<any>();
async function getQuoteLessThanThreeMaterialCode() {
  lessThanThreeMaterialRef.value?.show({
    sectionId: currentSectionId.value,
    quoteRound: hasQuotation.value,
    roundNo: searchData.value.roundNo,
  });
  // try {
  //   const res = await getQuoteLessThanThreeMaterialCodeList({
  //     noticeId: noticeId.value,
  //     sectionId: currentSectionId.value,
  //     quoteRound: hasQuotation.value,
  //   });
  //   if (res?.data?.length) {
  //     // TODO
  //     // await queryQuoteListByMaterialData({
  //     //   materialCodeList: res?.data,
  //     // });
  //     ElMessage.success('查询成功');
  //   } else {
  //     ElMessage.error('暂无数据');
  //   }
  //   // eslint-disable-next-line no-empty
  // } catch (error) {}
}

const searchPropsObj = computed(() => ({
  showCollapseBtn: false,
  collapse: false,
  showResetBtn: false,
  showSubmitBtn: false,
  showOperation: false,
  quantity: 3,
}));

const tablePropsObj = computed(() => ({
  ...tableProps.value,
  'span-method': objectSpanMethod,
  // cellStyle: calcCellStyle,
  // headerCellStyle: calcHeaderCellStyle,
  cellClassName: calcCellClassName,
  headerCellClassName: calcHeaderCellClassName,
  stripe: true,
  border: true,
  headerCellStyle: {
    backgroundColor: '#f5f7fa',
    color: '#303133',
    fontWeight: 'bold',
    // height: '40px',
    borderColor: '#EBEEF5',
    'vertical-align': 'middle',
  },
  cellStyle: {
    padding: '0',
    // height: '40px',
    'vertical-align': 'middle',
    borderColor: '#EBEEF5',
  },
  rowStyle: {
    // height: '40px',
    borderColor: '#EBEEF5',
  },
  // rowHeight: 40,
}));

const handleDownloadMaterialTemplate = async () => {
  try {
    const blob = await exportToExcel(tableData.value, dynamicColumn.value);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `物料数据_${moment().format('YYYY-MM-DD HH:mm:ss')}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    ElMessage.error('导出失败');
  }
};

// 标段变化事件
async function handleSectionChange(sectionId: string) {
  pageLoading.value = true;
  currentSectionId.value = sectionId;
  pageLoading.value = false;
  setTimeout(async () => {
    if (currentSectionId.value) {
      reLoad();
    }
  }, 300);
}
onMounted(async () => {});
onBeforeMount(async () => {});
defineExpose({
  handleSectionChange,
});
</script>

<style scoped lang="scss">
.document-review-container {
  flex: 1;
  padding: 20px;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  :deep(.dm-filter-root) {
    padding-right: 90px !important;
  }
  .right-button {
    position: absolute;
    z-index: 1;
    right: 0;
    top: 22px;
  }

  .table-container {
    position: relative;
    :deep(.quoteIp-header-cell-class-name .filter-box .filter-box-content .text-box) {
      color: var(--Color-Error-color-error, #ff3b30) !important;
    }
    :deep(.quoteIp-cell-class-name .cell) {
      background-color: rgba(255, 59, 48, 0.06) !important;
      color: var(---el-color-danger, #f53f3f) !important;
    }
    .alert-section {
      display: flex;
      padding: 8px 16px;
      align-items: center;
      gap: 8px;
      align-self: stretch;
      border-radius: var(--Radius-border-radius-small, 2px);
      background: var(--Color-Warning-color-warning-light-9, #fff9e8);
      color: var(--Color-Text-text-color-primary, #1d2129);
      font-family: 'PingFang SC';
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
    .common-ip-alert {
      border-radius: 4px;
      border: 1px solid var(--Color-Border-border-color-light, #e4e7ed);
      background: var(--color-white, #fff);
      /* light/box-shadow-lighter */
      box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.12);
      display: flex;
      padding: 8px 16px;
      align-items: center;
      position: relative;
      width: max-content;
      .alert-icon {
        width: 16px;
        height: 16px;
      }
      .alert-msg {
        color: var(--Color-Error-color-error, #ff3b30);
        /* regular/small */
        font-family: 'PingFang SC';
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 169.231% */
        padding-left: 8px;
        padding-right: 24px;
      }
      .alert-close {
        width: 12px;
        height: 12px;
        cursor: pointer;
      }
    }
  }
  :deep(.dm-table-header) {
    padding-top: 0;
    padding-bottom: 16px;
  }
  .right-section {
    color: var(--Color-Text-text-color-secondary, #86909c);
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    margin-left: 32px;
    .highlight {
      color: var(--Color-Text-text-color-primary, #1d2129);
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 600;
      line-height: 20px;
    }
  }
  .other-info-wrapper {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-top: 20px;
    gap: 40px;

    .info-left {
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 1;
      min-width: 0; /* 允许收缩 */
      overflow: hidden; /* 防止溢出 */
    }

    .info-right {
      display: flex;
      align-items: center;
      gap: 16px;
      flex-shrink: 0; /* 不允许收缩 */
    }

    .item-label {
      color: var(--Color-Text-text-color-primary, #1d2129);
      /* bold/medium */
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px; /* 150% */
      position: relative;
      padding-left: 10px;
      flex-shrink: 1; /* 允许收缩 */
      min-width: 0; /* 允许收缩到很小 */
      max-width: 200px; /* 限制最大宽度 */
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &::before {
        content: ' ';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 2px;
        height: 14px;
        background: var(--Color-Primary-color-primary, #0069ff);
        flex-shrink: 0; /* 前缀不收缩 */
      }
    }

    .item-block {
      display: inline-flex;
      align-items: center;
      border-radius: var(--Radius-border-radius-small, 2px);
      border: 1px solid var(--Color-Border-border-color-light, #e4e7ed);
      background: var(--color-white, #fff);
      height: 24px;
      padding: 2px 8px;
      box-sizing: border-box;
      gap: 8px;
      flex-shrink: 1; /* 允许收缩 */
      min-width: 0; /* 允许收缩 */

      &.file-item {
        max-width: 280px; /* 限制文件项的最大宽度 */
        min-width: 200px; /* 设置最小宽度确保下载按钮可见 */
        flex-shrink: 0; /* 文件项不允许收缩 */
      }

      /* 左侧信息块的特殊样式 */
      .info-left & {
        max-width: 200px; /* 限制左侧item-block的最大宽度 */
        flex-shrink: 1; /* 允许收缩 */
      }

      .label {
        color: var(--Color-Text-text-color-secondary, #86909c);
        /* regular/base */
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        flex-shrink: 0;
      }

      .value {
        color: var(--Color-Text-text-color-primary, #1d2129);
        /* regular/base */
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        flex-shrink: 1; /* 允许收缩 */

        &.file-value {
          max-width: 140px; /* 限制文件名显示宽度 */
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        &.ellipsis-value {
          max-width: 160px; /* 限制左侧value显示宽度 */
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          min-width: 0;
        }

        &:not(.file-value):not(.ellipsis-value) {
          flex-shrink: 0;
        }
      }

      .download-btn {
        color: var(--Color-Primary-color-primary, #0069ff);
        /* regular/base */
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        flex-shrink: 0; /* 下载按钮不允许收缩 */
        margin-left: 4px;
      }
    }
  }
}
</style>
