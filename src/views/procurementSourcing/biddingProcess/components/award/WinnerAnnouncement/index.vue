<template>
  <div
    class="winner-publicity-container"
    :class="{ 'change-mode': props.isChangeMode }"
  >
    <div class="bg-[#fff] p-5 mb-3">
      <!-- 标签栏 -->
      <StageTabs
        v-model="activeTabIndex"
        @update:sectionId="handleSectionChange"
      />

      <!-- 中标信息表格 -->
      <el-table
        :data="winnerTableData"
        :loading="tableLoading"
        style="width: 100%"
        class="editable-table mt-4"
        v-loading="tableLoading"
      >
        <el-table-column
          label="序号"
          type="index"
          width="60"
          align="center"
        />
        <el-table-column
          label="供应商名称"
          prop="supplierName"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          label="供应商编号"
          prop="supplierCode"
          width="140"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="联系人"
          prop="contactName"
          width="120"
          align="center"
        />
        <el-table-column
          label="联系方式"
          prop="contactPhone"
          width="140"
          align="center"
        />
        <el-table-column
          label="中标总金额"
          prop="totalAwardedAmount"
          width="130"
          align="center"
        >
          <template #default="{ row }">
            <span>{{ formatPrice(row.totalAwardedAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="中标物料数量"
          prop="awardedMaterialCount"
          width="120"
          align="center"
        />
        <el-table-column
          label="中标总数量"
          prop="totalAwardedQuantity"
          width="120"
          align="center"
        />
        <el-table-column
          label="供应商类型"
          prop="supplierType"
          width="120"
          align="center"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ SUPPLY_TYPE_OBJ[row.supplierType] || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          label="企业性质"
          prop="enterpriseNature"
          width="120"
          align="center"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ getBusinessNature(row.enterpriseNature) || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          label="法人代表"
          prop="legalPerson"
          width="120"
          align="center"
        />
        <el-table-column
          label="操作"
          width="140"
          align="center"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="text"
              @click="handleViewDetail(row)"
            >
              查看中标明细
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 表单区域 -->
    <el-form
      ref="publicityFormRef"
      :model="publicityForm"
      :rules="formRules"
      label-width="120px"
      class="publicity-form"
    >
      <!-- 公告要求 -->
      <div class="publicity-requirements-section">
        <div class="section-header">
          <div class="header-title">公告要求</div>
        </div>

        <el-form-item
          label="是否发布公告"
          prop="publishType"
        >
          <el-radio-group
            v-model="publicityForm.publishType"
            @change="handlePublishTypeChange"
          >
            <el-radio label="system">系统发布公告</el-radio>
            <el-radio label="manual">不发布中标公告</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 只有选择"系统发布公告"时才显示以下字段 -->
        <template v-if="publicityForm.publishType === 'system'">
          <el-row>
            <el-col :span="8">
              <el-form-item
                label="公告标题"
                prop="title"
              >
                <el-input
                  v-model="publicityForm.title"
                  placeholder="请输入公告标题"
                  maxlength="50"
                  show-word-limit
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </div>

      <!-- 公告详情 - 只有选择"系统发布公告"时才显示 -->
      <div
        v-if="publicityForm.publishType === 'system'"
        class="publicity-details-section"
      >
        <div class="section-header">
          <div class="header-title">公告详情</div>
        </div>

        <el-row>
          <el-col :span="8">
            <el-form-item
              label="引用模版"
              prop="templateId"
            >
              <el-select
                v-model="publicityForm.templateId"
                placeholder="请选择公告模版"
                style="width: 200px"
                @change="handleTemplateChange"
              >
                <el-option
                  v-for="template in templateOptions"
                  :key="template.id"
                  :label="template.templateName"
                  :value="template.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item
          v-if="isEditMode"
          label="公告内容"
        >
          <el-link
            type="text"
            @click="handleEditContent"
            class="edit-button"
          >
            <el-icon class="mr-[6px]"><edit-pen /></el-icon>
            查看
          </el-link>
        </el-form-item>
      </div>

      <!-- 公告附件 - 只有选择"系统发布公告"时才显示 -->
      <div
        v-if="publicityForm.publishType === 'system'"
        class="publicity-attachments-section"
      >
        <div class="section-header">
          <div class="header-title">公告附件</div>
        </div>

        <el-form-item
          v-if="!isEditMode"
          label="公告附件"
        >
          <!-- 非编辑模式：显示上传组件 -->
          <div class="upload-wrapper">
            <YunUpload
              v-model="publicityFileList"
              @change="handleUploadChange"
              :limit="5"
              :multiple="true"
            />
          </div>
        </el-form-item>
        <!-- 编辑模式：显示文件下载列表 -->
        <div v-if="isEditMode">
          <div class="flex gap-4 mt-3">
            <div class="file-label">定标公告附件：</div>
            <div
              class="flex flex-col gap-2"
              v-if="publicityFileList.length > 0"
            >
              <div
                class="flex gap-4 items-center"
                v-for="(item, index) in publicityFileList"
                :key="index"
              >
                <div
                  class="file-item-label"
                  @click="handleDownload(item)"
                >
                  {{ item.fileName || item.name }}
                </div>
              </div>
            </div>
            <div
              v-else
              class="no-files"
            >
              <span>暂无附件</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 公告平台 - 只有选择"系统发布公告"时才显示 -->
      <div
        v-if="publicityForm.publishType === 'system'"
        class="publicity-platform-section"
      >
        <div class="section-header">
          <div class="header-title">公告平台</div>
        </div>

        <el-form-item label="公告平台选择">
          <div class="platform-checkboxes">
            <el-checkbox-group v-model="publicityForm.platforms">
              <el-checkbox
                v-for="platform in platformOptions"
                :key="platform.value"
                :label="platform.value"
              >
                {{ platform.label }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>
      </div>

      <!-- 审批设置 -->
      <div class="publicity-approval-section">
        <div class="section-header">
          <div class="header-title">审批设置</div>
        </div>

        <el-form-item
          label="发起审批"
          prop="specialProcessExecutorList"
        >
          <div class="flex items-center gap-2">
            <el-radio-group
              v-model="approvalData.approvalType"
              @change="handleApprovalTypeChange"
            >
              <el-radio :label="0">系统自动发起</el-radio>
              <el-radio :label="1">指定审批人</el-radio>
            </el-radio-group>
            <UserSelector
              v-model="approvalData.specialProcessExecutorList"
              v-show="approvalData.approvalType === 1"
              :multiple="true"
              style="width: auto !important"
              placeholder="请选择审批人"
              @update:model-value="handleApprovalDataChange"
            />
          </div>
        </el-form-item>
      </div>
    </el-form>
  </div>

  <div
    v-if="!isEditMode && !props.isChangeMode && isProjectLeader"
    class="form-actions-wrapper"
  >
    <div class="form-actions">
      <el-button
        type="primary"
        @click="handleSubmit"
      >
        {{ '生成中标公告' }}
      </el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </div>

  <!-- 公告内容编辑抽屉 -->
  <AnnouncementEditor
    ref="announcementEditorRef"
    :readonly="isEditMode"
    @save="handleSaveContent"
  />

  <!-- 中标明细抽屉 -->
  <AwardDetailDrawer
    v-model:visible="awardDetailDrawerVisible"
    :supplier-data="selectedSupplierData"
  />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { EditPen } from '@element-plus/icons-vue';
import StageTabs from '@/views/procurementSourcing/biddingProcess/components/bidding/StageTabs.vue';
import AnnouncementEditor from '../AwardResult/components/AnnouncementEditor.vue';
import YunUpload from '@/components/YunUpload/index.vue';
import UserSelector from '@/components/UserSelector/index.vue';
import { getTemplateList, getTenderBidById } from '../../../api/award';
import type { TemplateOption, TenderBidPublicityDetail, TenderBidDetailResponse } from '../../../types/award';
import { getAwardedSuppliers, saveWinnerAnnouncement } from '../../../api/WinnerAnnouncement';
import type { AwardedSupplierInfo, InviteSupplierInfo } from '../../../types/winnerAnnouncement';
import { getTemplateDetail } from '../../../api/announcement';
import { templateContentFormatter } from '../../announcement/ProcurementDocument/templateFormatter';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import moment from 'moment';
import { getNoticeChangeDetailInfo } from '@/api/purchasing/noticeChange';
import { SUPPLY_TYPE_OBJ } from '@/views/lowcode/supply/const';
import AwardDetailDrawer from '@/views/procurementSourcing/biddingProcess/components/award/WinnerPublicity/AwardDetailDrawer.vue';
import downloadUrlFile from '@/utils/downloadUrl.js';
import { BUSINESS_NATURE_OPTIONS } from '@/api/supplier';

// 定义组件 Props
interface Props {
  isChangeMode?: boolean; // 是否为变更模式
  formData?: any; // 变更模式下的表单数据
  isViewMode?: boolean; // 是否为查看模式
}

const props = withDefaults(defineProps<Props>(), {
  isChangeMode: false,
  formData: null,
  isViewMode: false,
});

const emit = defineEmits<{
  (e: 'update:form-data', data: any): void;
  (e: 'change-submit', data: { content: string; submitData: any }): void;
}>();

const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);
const projectDetail = computed(() => biddingStore?.projectDetail);
const isProjectLeader = computed(() => biddingStore?.isProjectLeader);

// 定义前端使用的附件信息接口
interface FrontendAttachmentInfo {
  id: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  fileType: string;
}

// 定义公告表单数据结构
interface AnnouncementFormData {
  noticeId: number;
  projectId: number;
  publishType: string; // 发布类型：'system' | 'manual'
  title: string; // 公告标题
  templateId: string; // 引用模版ID
  content: string; // 公告内容
  attachmentInfos: FrontendAttachmentInfo[]; // 附件信息
  platforms: string[]; // 公告平台
}

// 表单引用
const publicityFormRef = ref<FormInstance>();

// 抽屉组件引用
const announcementEditorRef = ref();

const activeTabIndex = ref(0);

// 当前选中的标段ID
const currentSectionId = ref<number | null>(null);

// 中标信息表格数据（原始数据）
const allWinnerData = ref<AwardedSupplierInfo[]>([]);

// 过滤后的表格数据（用于显示）
const winnerTableData = ref<AwardedSupplierInfo[]>([]);

// 表格加载状态
const tableLoading = ref(false);

// 公告表单数据 - 固定写死 noticeId 和 projectId
const publicityForm = reactive<AnnouncementFormData>({
  noticeId: noticeId.value, // 固定写死的公告ID
  projectId: projectId.value, // 固定写死的采购立项ID
  publishType: 'system', // 默认选中"系统发布公告"
  title: '',
  templateId: '',
  content: '',
  attachmentInfos: [],
  platforms: [],
});

// 审批数据
const approvalData = reactive({
  approvalType: 0, // 0: 系统自动发起, 1: 指定审批人
  specialProcessExecutorList: [], // 指定审批人列表
});

// 保存状态
const saving = ref(false);

// 页面状态：编辑模式还是新增模式
const isEditMode = ref(false);

// 定标详情数据
const tenderBidDetail = ref<TenderBidPublicityDetail | null>(null);

// 表单验证规则
const formRules: FormRules = {
  publishType: [{ required: true, message: '请选择是否发布公告', trigger: 'change' }],
  title: [
    {
      required: true,
      message: '请输入公告标题',
      trigger: 'blur',
    },
    {
      validator: (rule, value, callback) => {
        // 只有选择系统发布公告时才验证
        if (publicityForm.publishType !== 'system') {
          callback();
          return;
        }

        // 检查实际的标题值
        const titleValue = publicityForm.title;
        if (!titleValue || titleValue.trim() === '') {
          callback(new Error('请输入公告标题'));
          return;
        }

        if (titleValue.length > 50) {
          callback(new Error('公告标题不能超过50个字符'));
          return;
        }

        callback();
      },
      trigger: ['blur', 'change'],
    },
  ],
  templateId: [
    {
      required: true,
      message: '请选择引用模版',
      trigger: 'change',
    },
    {
      validator: (rule, value, callback) => {
        // 只有选择系统发布公告时才验证
        if (publicityForm.publishType !== 'system') {
          callback();
          return;
        }

        // 检查实际的模板值
        const templateValue = publicityForm.templateId;
        if (!templateValue || templateValue === '') {
          callback(new Error('请选择引用模版'));
          return;
        }

        callback();
      },
      trigger: ['change'],
    },
  ],
  content: [
    {
      required: true,
      message: '请编辑公告内容',
      trigger: 'blur',
    },
    {
      validator: (rule, value, callback) => {
        // 只有选择系统发布公告时才验证
        if (publicityForm.publishType !== 'system') {
          callback();
          return;
        }

        // 检查实际的公告内容值
        const contentValue = publicityForm.content;
        if (!contentValue || contentValue.trim() === '') {
          callback(new Error('请编辑公告内容'));
          return;
        }

        callback();
      },
      trigger: ['blur', 'change'],
    },
  ],
  specialProcessExecutorList: [
    {
      required: true,
      message: '请选择审批人',
      trigger: 'change',
      validator: (rule: any, value: any, callback: any) => {
        // 检查审批类型和审批人列表
        if (approvalData.approvalType === 1) {
          // 检查 approvalData.specialProcessExecutorList 而不是 value
          if (!approvalData.specialProcessExecutorList || approvalData.specialProcessExecutorList.length === 0) {
            callback(new Error('请选择审批人'));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
    },
  ],
};

// 模板选项
const templateOptions = ref<TemplateOption[]>([]);

// 平台选项
const platformOptions = ref([
  { label: '中国采购与招标网', value: 'procurement' },
  { label: '中国招标投标公共服务平台', value: 'quality-procurement' },
]);

// 公告附件文件列表
const publicityFileList = ref<any[]>([]);

// 处理发布类型切换
function handlePublishTypeChange(value: string) {
  // 清除所有表单验证错误
  if (publicityFormRef.value) {
    publicityFormRef.value.clearValidate();
  }

  // 如果切换为不发布公告，清空相关字段
  if (value === 'manual') {
    publicityForm.title = '';
    publicityForm.content = '';
    publicityForm.attachmentInfos = [];
    publicityForm.platforms = [];
  }
}

// 处理标段变化
function handleSectionChange(sectionId: number) {
  currentSectionId.value = sectionId;
  filterWinnerDataBySectionId();
}

// 根据标段ID过滤中标供应商数据
function filterWinnerDataBySectionId() {
  if (currentSectionId.value === null) {
    winnerTableData.value = allWinnerData.value;
  } else {
    winnerTableData.value = allWinnerData.value.filter((item) => item.sectionId === currentSectionId.value);
  }
}

// 格式化价格
function formatPrice(price: number): string {
  return `¥${price.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

// 处理文件上传变化
function handleUploadChange(fileList: any[]) {
  // 更新附件信息数组，使用fileName和filePath作为属性名
  publicityForm.attachmentInfos = fileList.map((file) => ({
    id: file.id || Date.now().toString(),
    fileName: file.name,
    filePath: file.url || '',
    fileSize: file.size || 0,
    fileType: file.type || '',
  }));
}

const handleDownload = (item: any) => {
  downloadUrlFile(item.filePath, item.fileName);
};

// 中标明细抽屉相关
const awardDetailDrawerVisible = ref(false);
const selectedSupplierData = ref<AwardedSupplierInfo | undefined>(undefined);

// 处理查看中标明细
function handleViewDetail(row: AwardedSupplierInfo) {
  selectedSupplierData.value = row;
  awardDetailDrawerVisible.value = true;
}

// 验证必要数据
function validateRequiredData(): boolean {
  // 1. 检查是否有中标供应商数据
  if (!allWinnerData.value || allWinnerData.value.length === 0) {
    ElMessage.error('请先获取中标供应商数据');
    return false;
  }

  // 2. 检查是否有具有有效金额的中标项目
  const validAwardedItems = allWinnerData.value;
  // .filter(item =>
  //   item.totalAwardedAmount && item.totalAwardedAmount > 0
  // );

  if (validAwardedItems.length === 0) {
    ElMessage.error('无有效的中标数据，请确认中标信息');
    return false;
  }

  // 3. 检查是否选择了模板（当选择发布公告时）
  if (publicityForm.publishType === 'system' && !publicityForm.templateId) {
    ElMessage.error('请选择引用模版');
    return false;
  }

  return true;
}

// 验证模板选择并获取模板内容
async function verifyTemplateSelection(): Promise<boolean> {
  if (publicityForm.publishType !== 'system') {
    return true; // 如果不发布公告，跳过模板验证
  }

  if (!publicityForm.templateId) {
    ElMessage.error('请先选择引用模版');
    return false;
  }

  try {
    const { data } = await getTemplateDetail(publicityForm.templateId);
    if (!data || !data.content) {
      ElMessage.error('模板内容为空，请选择其他模板');
      return false;
    }
    return true;
  } catch (error) {
    return false;
  }
}

const getBusinessNature = (value: string) => {
  return BUSINESS_NATURE_OPTIONS.find((item) => item.value === value)?.label || '-';
};

// 处理审批类型变化
const handleApprovalTypeChange = (value: number) => {
  approvalData.approvalType = value;
  if (value === 0) {
    // 系统自动发起时清空审批人列表
    approvalData.specialProcessExecutorList = [];
  }

  // 触发审批人字段的验证
  nextTick(() => {
    if (publicityFormRef.value) {
      publicityFormRef.value.validateField('specialProcessExecutorList');
    }
  });
};

// 处理审批数据变化
const handleApprovalDataChange = (userIds: string[] | string) => {
  // update:modelValue 事件可能返回 userId 数组或单个 userId
  if (Array.isArray(userIds)) {
    approvalData.specialProcessExecutorList = userIds;
  } else if (typeof userIds === 'string' && userIds) {
    approvalData.specialProcessExecutorList = [userIds];
  } else {
    approvalData.specialProcessExecutorList = [];
  }
  console.log('Processed approval data:', approvalData.specialProcessExecutorList);

  // 触发审批人字段的验证
  nextTick(() => {
    if (publicityFormRef.value) {
      publicityFormRef.value.validateField('specialProcessExecutorList');
    }
  });
};

// 转换数据为模板变量格式
function transformAwardDataForTemplate() {
  // 1. 按供应商分组统计
  const supplierGroupData = allWinnerData.value.reduce((acc, item) => {
    const key = `${item.supplierName}_${item.sectionId}`;
    if (!acc[key]) {
      acc[key] = {
        supplierName: item.supplierName,
        supplierCode: item.supplierCode,
        contactName: item.contactName,
        contactPhone: item.contactPhone,
        materials: [],
        totalAmount: 0,
      };
    }
    acc[key].materials.push(item);
    acc[key].totalAmount += item.totalAwardedAmount || 0;
    return acc;
  }, {} as Record<string, any>);

  // 2. 计算汇总数据
  const totalAwardAmount = allWinnerData.value.reduce((sum, item) => sum + (item.totalAwardedAmount || 0), 0);
  const totalSupplierCount = Object.keys(supplierGroupData).length;
  const totalMaterialCount = allWinnerData.value.length;

  // 3. 构建模板数据对象
  return {
    // 项目基本信息
    noticeTitle: publicityForm.title || '中标公告',

    projectName: projectDetail.value?.projectName || '',
    projectCode: projectDetail.value?.projectCode || '',
    procurementMethod: projectDetail.value?.procurementMethod || '',
    budgetAmount: projectDetail.value?.budgetAmount || 0,
    // 统计数据
    totalAwardAmount: totalAwardAmount,
    totalSupplierCount: totalSupplierCount,
    totalMaterialCount: totalMaterialCount,

    // 格式化金额
    formattedTotalAmount: `¥${totalAwardAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`,

    // 供应商列表
    supplierList: Object.values(supplierGroupData),
    // 时间信息
    generateTime: moment().format('YYYY-MM-DD HH:mm:ss'),
    currentDate: moment().format('YYYY年MM月DD日'),
  };
}

// 处理模板变化事件
async function handleTemplateChange() {
  if (!publicityForm.templateId) return;

  try {
    // 检查是否已有内容且不是默认内容
    if (!publicityForm.content || publicityForm.content.includes('<h2>XDMY-LYMG')) {
      const { data } = await getTemplateDetail(publicityForm.templateId);
      publicityForm.content = data.content;
    }
  } catch (error) {}
}

// 处理编辑公告内容
async function handleEditContent() {
  // 检查是否选择了模板
  if (!publicityForm.templateId) {
    ElMessage.error('请先选择公告模板');
    return;
  }

  try {
    // 编辑模式：直接显示已保存的内容
    if (isEditMode.value) {
      if (!publicityForm.content) {
        ElMessage.error('暂无公告内容');
        return;
      }

      // 直接打开富文本编辑器，显示已保存的内容
      announcementEditorRef.value?.show(publicityForm.content, '编辑中标公告');
      return;
    }

    // 非编辑模式：需要进行模板转换和数据填充
    // 1. 先校验数据
    if (!validateRequiredData()) {
      return;
    }

    // 2. 转换数据为模板格式
    const templateData = transformAwardDataForTemplate();

    // 3. 获取模板内容（如果还没有的话）
    if (!publicityForm.content || publicityForm.content.includes('<h2>XDMY-LYMG')) {
      const { data } = await getTemplateDetail(publicityForm.templateId);
      publicityForm.content = data.content;
    }

    // 4. 使用模板格式化器处理内容
    const formattedContent = templateContentFormatter(publicityForm.content, templateData, {
      removeEmptyLoops: true,
      removeEmptyTags: true,
      debug: true,
    });

    // 5. 打开富文本编辑器，并设置格式化后的内容
    announcementEditorRef.value?.show(formattedContent, '编辑中标公告');
  } catch (error) {
    console.error('生成公告内容失败:', error);
  }
}

// 处理保存公告内容
async function handleSaveContent(content: string) {
  if (isEditMode.value) {
    return;
  }

  // 变更模式下，通过 emit 通知父组件处理
  if (props.isChangeMode) {
    // 1. 先校验数据
    if (!validateRequiredData()) {
      return;
    }

    // 2. 验证表单
    if (publicityFormRef.value) {
      await publicityFormRef.value.validate();
    }

    // 3. 更新公告内容
    publicityForm.content = content;

    // 4. 组装提交数据（使用相同的转换器）
    const submitData = assembleSubmitData();

    // 5. 通知父组件处理变更提交
    emit('change-submit', {
      content,
      submitData,
    });
    return;
  }

  // 原有的公告模式逻辑
  try {
    saving.value = true;

    // 1. 验证必要数据
    if (!validateRequiredData()) {
      return;
    }

    // 2. 验证表单
    if (publicityFormRef.value) {
      await publicityFormRef.value.validate();
    }

    // 3. 更新内容
    publicityForm.content = content;

    // 4. 准备提交数据
    const submitData = assembleSubmitData();

    // 5. 调用接口保存
    const { code, msg } = await saveWinnerAnnouncement(submitData);

    if (code === 0) {
      ElMessage.success(isEditMode.value ? '中标公告更新成功' : '中标公告保存成功');

      // 提交成功后重新加载数据
      if (!isEditMode.value) {
        // 如果是新增模式，提交成功后切换到编辑模式
        await loadTenderBidDetail();
      }
    } else {
      ElMessage.error(msg || (isEditMode.value ? '更新失败' : '保存失败'));
    }
  } catch (error) {
    console.error('保存失败:', error);
  } finally {
    saving.value = false;
  }
}

// 从列表数据中提取并去重邀请供应商信息
function getInviteSupplierList(): InviteSupplierInfo[] {
  // 创建一个Map来去重，key为 sectionId + tenantSupplierId 的组合
  const supplierMap = new Map<string, InviteSupplierInfo>();

  allWinnerData.value.forEach((supplier) => {
    const key = `${supplier.sectionId}_${supplier.tenantSupplierId}`;

    // 如果还没有这个供应商，或者当前数据更完整，则更新
    if (!supplierMap.has(key)) {
      supplierMap.set(key, {
        sectionId: supplier.sectionId,
        tenantSupplierId: supplier.tenantSupplierId,
        supplierName: supplier.supplierName,
        contactPerson: supplier.contactName,
        contactPhone: supplier.contactPhone,
        dealPrice: supplier.totalAwardedAmount,
      });
    }
  });

  return Array.from(supplierMap.values());
}

// 组装提交数据
function assembleSubmitData() {
  // 获取去重后的邀请供应商列表
  const inviteSupplierList = getInviteSupplierList();

  // 转换附件信息格式，符合接口要求
  const attachmentInfos = publicityForm.attachmentInfos.map((file) => ({
    businessId: publicityForm.noticeId, // 使用公告ID作为业务ID
    businessType: 'NOTICE', // 业务类型：公告
    fileName: file.fileName,
    filePath: file.filePath,
    fileType: file.fileType,
    fileSize: file.fileSize,
  }));

  // 基础数据结构，符合接口要求
  const submitData = {
    noticeId: publicityForm.noticeId,
    projectId: publicityForm.projectId,
    inviteSupplierList,
    isPublishNotice: publicityForm.publishType === 'system', // 转换为boolean
    noticeTitle: publicityForm.publishType === 'system' ? publicityForm.title : '',
    noticeTemplateId: publicityForm.publishType === 'system' ? parseInt(publicityForm.templateId) || 0 : 0,
    noticeContent: publicityForm.publishType === 'system' ? publicityForm.content : '',
    noticeAttachments: publicityForm.publishType === 'system' ? attachmentInfos : [],
  };

  return submitData;
}

// 处理提交
async function handleSubmit() {
  try {
    // 1. 验证必要数据
    if (!validateRequiredData()) {
      return;
    }

    // 2. 验证表单（包括审批设置）
    if (publicityFormRef.value) {
      await publicityFormRef.value.validate();
    }

    // 3. 检查是否选择不发布公告
    if (publicityForm.publishType === 'manual') {
      // 不发布公告：直接保存数据，不打开模板编辑器
      try {
        saving.value = true;

        // 清空模板相关字段
        publicityForm.templateId = '';
        publicityForm.content = '';
        publicityForm.title = '';

        // 准备提交数据
        const submitData = assembleSubmitData();

        // 变更模式下，通过 emit 通知父组件处理
        if (props.isChangeMode) {
          // 通知父组件处理变更提交
          emit('change-submit', {
            content: '',
            submitData,
          });
          return;
        }

        // 非变更模式：直接调用接口保存
        const { code, msg } = await saveWinnerAnnouncement(submitData);

        if (code === 0) {
          ElMessage.success('中标公告保存成功');
          // 提交成功后重新加载数据
          await loadTenderBidDetail();
        } else {
          ElMessage.error(msg || '保存失败');
        }
      } catch (error) {
        console.error('保存失败:', error);
        ElMessage.error('保存失败');
      } finally {
        saving.value = false;
      }
      return;
    }

    // 4. 验证模板选择（只有发布公告时才验证）
    if (!(await verifyTemplateSelection())) {
      return;
    }

    // 5. 转换数据为模板变量格式
    const templateData = transformAwardDataForTemplate();

    // 6. 获取并格式化模板内容
    if (!publicityForm.content || publicityForm.content.includes('<h2>XDMY-LYMG')) {
      const { data } = await getTemplateDetail(publicityForm.templateId);
      publicityForm.content = data.content;
    }

    const formattedContent = templateContentFormatter(publicityForm.content, templateData, {
      removeEmptyLoops: true,
      removeEmptyTags: true,
      debug: true,
    });

    // 7. 打开富文本编辑器进行最终编辑和保存
    announcementEditorRef.value?.show(formattedContent, '编辑中标公告');
  } catch (error) {
    console.error('数据转换失败:', error);
  }
}

// 处理取消
function handleCancel() {}

// 获取中标供应商列表
async function getWinnerData() {
  try {
    tableLoading.value = true;
    const { data } = await getAwardedSuppliers(publicityForm.noticeId, publicityForm.projectId);

    if (data && Array.isArray(data)) {
      // 存储原始数据
      allWinnerData.value = data;
      // 根据当前选中的标段进行过滤
      filterWinnerDataBySectionId();
    } else {
      allWinnerData.value = [];
      winnerTableData.value = [];
    }
  } catch (error) {
    allWinnerData.value = [];
    winnerTableData.value = [];
  } finally {
    tableLoading.value = false;
  }
}

// 获取模版列表
async function getTemplateOptions() {
  try {
    const { data } = await getTemplateList({
      type: 'AWARD_ANNOUNCEMENT', // 中标公告模版类型
    });

    if (data?.records && data.records.length > 0) {
      templateOptions.value = data.records;
      if (publicityForm.publishType === 'system' && !publicityForm.templateId) {
        publicityForm.templateId = data.records[0]?.id || '';
      }
    }
  } catch (error) {
    console.error('获取模版列表失败:', error);
  }
}

// 查询定标详情
async function loadTenderBidDetail() {
  try {
    const service = props.isViewMode ? getNoticeChangeDetailInfo : getTenderBidById;
    const params = {
      noticeId: noticeId.value,
      projectId: projectId.value,
      changeTypeEnum: 'TO_APPROVE',
    };

    const response = props.isViewMode ? await service(params) : await service(publicityForm.noticeId, publicityForm.projectId);

    if (response.code === 0 && response.data) {
      tenderBidDetail.value = response.data;

      if (props.isViewMode) {
        isEditMode.value = false;
      } else if (tenderBidDetail.value?.noticeContent) {
        isEditMode.value = true;
      } else {
        isEditMode.value = false;
      }

      // 回填中标公告数据
      fillAnnouncementFormData(response.data);
    } else {
      // 没有数据，显示新增模式
      isEditMode.value = false;
    }
  } catch (error) {
    // 查询失败也显示新增模式
    isEditMode.value = false;
  }
}

// 回填中标公告表单数据
function fillAnnouncementFormData(data: TenderBidPublicityDetail) {
  // 回填公告基本信息
  if (data.noticeStatus && data.noticeStatus !== 'UNPUBLISHED') {
    publicityForm.publishType = 'system';
  } else {
    publicityForm.publishType = 'manual';
  }

  // 回填公告模板
  if (data.noticeTemplateId) {
    publicityForm.templateId = data.noticeTemplateId.toString();
  }

  // 回填公告内容
  if (data.noticeContent) {
    publicityForm.content = data.noticeContent;
  }

  // 回填公告标题
  if (data.noticeTitle) {
    publicityForm.title = data.noticeTitle;
  }

  // 回填附件信息
  if (data.noticeAttachments && data.noticeAttachments.length > 0) {
    const attachmentData = data.noticeAttachments.map((file: any) => ({
      id: file.id || Date.now().toString(),
      fileName: file.fileName || file.name,
      filePath: file.filePath || file.url,
      fileSize: file.fileSize || file.size || 0,
      fileType: file.fileType || file.type || '',
    }));

    publicityForm.attachmentInfos = attachmentData;
    publicityFileList.value = data.noticeAttachments.map((file: any) => ({
      id: file.id || Date.now().toString(),
      name: file.fileName || file.name,
      filePath: file.filePath || file.url,
      size: file.fileSize || file.size || 0,
      type: file.fileType || file.type || '',
    })) as any[];
  }
}

// 变更模式专用的验证和提交方法
const validateAnnouncementData = async (): Promise<boolean> => {
  try {
    await handleSubmit();
    return true;
  } catch (error) {
    console.error('验证失败:', error);
    return false;
  }
};

// 获取当前组件的数据用于变更提交
const getAnnouncementData = () => {
  return {
    publicityForm: { ...publicityForm },
    allWinnerData: [...allWinnerData.value],
    winnerTableData: [...winnerTableData.value],
  };
};

// 变更模式下的数据初始化（只在初始化时从父组件接收数据，避免循环更新）
if (props.isChangeMode && props.formData) {
  // 仅在初始化时回填数据
  const initData = props.formData;
  if (initData.publicityForm) Object.assign(publicityForm, initData.publicityForm);
  if (initData.allWinnerData) allWinnerData.value = [...initData.allWinnerData];
  if (initData.winnerTableData) winnerTableData.value = [...initData.winnerTableData];
}

// 初始化函数
async function init() {
  // 获取模版列表
  getTemplateOptions();

  loadTenderBidDetail();

  getWinnerData();
}

// 暴露方法给父组件调用
defineExpose({
  validateAnnouncementData,
  getAnnouncementData,
  handleSaveContent,
  handleSubmit,
});

// 组件挂载时初始化
onMounted(() => {
  init();
});
</script>

<style lang="scss" scoped>
@import '../../../styles/collapse-panel.scss';
.page-header {
  margin-bottom: 16px;

  .company-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .company-icon {
      color: #0069ff;
      font-size: 18px;
    }

    .company-name {
      font-size: 18px;
      font-weight: 600;
      color: #1d2129;
    }

    .status-tag {
      margin-left: 8px;
    }
  }
}

.winner-table-section,
.publicity-requirements-section,
.publicity-details-section,
.publicity-attachments-section,
.publicity-platform-section,
.publicity-approval-section {
  margin-bottom: 12px;
  background: #fff;
  border-radius: 6px;
  padding: 16px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .header-title {
      font-size: 16px;
      font-weight: 600;
      color: #1d2129;
    }
  }
}

.change-mode {
  .winner-table-section,
  .publicity-requirements-section,
  .publicity-details-section,
  .publicity-attachments-section,
  .publicity-platform-section,
  .publicity-approval-section {
     margin-bottom: 0 !important; 
     padding: 16px 16px 0 16px !important;
  }
}

.publicity-form {
  :deep(.el-form-item) {
    margin-bottom: 20px;
  }
}

.winner-table {
  :deep(.el-table__header) {
    th {
      background: #f5f7fa;
      padding: 12px 0;
      border-bottom: 1px solid #ebeef5;

      .cell {
        color: #505762;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }

  :deep(.el-table__body) {
    tr {
      td {
        border-bottom: 1px solid #ebeef5;
        padding: 12px 0;
      }
    }
  }
}

.platform-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.upload-wrapper {
  width: 100%;
}

.file-label {
  color: var(--Color-Text-text-color-regular, #4e5969);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}

.file-item-label {
  color: var(--Color-Primary-color-primary, #0069ff);
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
}

:deep(.el-range-input) {
  background: #fff;
}

.form-actions-wrapper {
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  z-index: 1000;
  position: sticky;
  bottom: -20px;
  left: 0;
  right: 0;
  padding: 16px 24px;
  display: flex;
  gap: 12px;
  border-top: 1px solid var(--Color-Border-border-color-light, #e4e7ed);

  .form-actions {
    display: flex;
    justify-content: flex-start;
    gap: 12px;
    width: 100%;
  }
}

.edit-button {
  color: var(--Color-Primary-color-primary, #0069ff);

  &:hover {
    color: #1677ff;
  }
}

.header-title {
  color: var(--Color-Text-text-color-primary, #1d2129);
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
  padding-left: 10px;
  position: relative;
  &::before {
    content: '';
    display: inline-block;
    width: 2px;
    height: 14px;
    background: var(--Color-Primary-color-primary, #0069ff);
    margin-right: 8px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}
</style>
