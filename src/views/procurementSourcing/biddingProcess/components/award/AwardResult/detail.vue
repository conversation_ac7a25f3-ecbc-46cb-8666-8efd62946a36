<template>
  <div
    class="award-result-container"
    :class="{ 'change-mode': props.isChangeMode }"
  >
    <!-- 定标基本信息 - 只有在有定标详情时才显示 -->
    <div
      v-if="isEditMode"
      class="award-info-section"
    >
      <div class="award-info-content">
        <div class="info-item">
          <div class="info-label">定标人</div>
          <div class="info-value">{{ awardInfo.awardPerson }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">定标时间</div>
          <div class="info-value">{{ awardInfo.awardTime }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">审批状态</div>
          <div class="info-value">
            <el-tag
              :type="getApprovalStatusType(approvalStatus)"
              size="small"
            >
              {{ getApprovalStatusText(approvalStatus) }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 定标清单 -->
    <div class="award-list-section">
      <div class="section-header">
        <div class="header-title">定标清单</div>
      </div>

      <div class="mt-4"></div>
      <StageTabs
        v-if="!props.sectionId"
        v-model="activeTabIndex"
        @update:sectionId="handleSectionChange"
      />

      <div class="flex justify-between items-center my-4">
        <div class="flex items-center gap-4">
          <el-radio-group
            v-if="isEditMode && !isZJWT"
            v-model="viewMode"
            @change="changeViewMode"
          >
            <el-radio-button label="material">按物料查看</el-radio-button>
            <el-radio-button label="supplier">按供应商查看</el-radio-button>
          </el-radio-group>
          <div class="font-[12px]">
            已发起
            <span class="award-text-primary">{{ roundNum }}</span>
            轮报价
          </div>
        </div>
      </div>
      <!-- 筛选区域 -->
      <div
        class="filter-section flex justify-between items-center"
        v-if="viewMode === 'material'"
      >
        <el-form
          :inline="true"
          :model="materialFilters"
          class="filter-form"
        >
          <el-form-item
            label="供应商名称"
            label-width="120px"
          >
            <el-input
              v-model="materialFilters.supplierName"
              placeholder="请输入供应商名称"
              clearable
              style="width: 263px"
              @input="handleSupplierNameInput"
            />
          </el-form-item>
        </el-form>
        <el-button
          v-if="isEditMode"
          @click="handleExportMaterial"
          :loading="exportLoading"
        >
          导出
          <svg
            class="ml-1"
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 14 14"
            fill="none"
          >
            <path
              d="M6.99968 1.50839L10.4496 4.95835L9.62468 5.78331L7.58301 3.74164L7.58301 9.33335H6.41634L6.41634 3.74164L4.37468 5.78331L3.54972 4.95835L6.99968 1.50839ZM2.62467 8.16668V11.0834H11.3747V8.16668H12.5413V12.25H1.45801V8.16668H2.62467Z"
              fill="#4E5969"
            />
          </svg>
        </el-button>
      </div>

      <div
        class="filter-section flex justify-between items-center"
        v-if="isEditMode && viewMode === 'supplier'"
      >
        <el-form
          :inline="true"
          :model="supplierFilters"
          class="filter-form"
        >
          <el-form-item label="供应商名称">
            <el-input
              v-model="supplierFilters.supplierName"
              placeholder="请输入供应商名称"
              clearable
              style="width: 263px"
            />
          </el-form-item>
        </el-form>
        <el-button
          @click="handleExportSupplier"
          :loading="exportLoading"
        >
          导出
          <svg
            class="ml-1"
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 14 14"
            fill="none"
          >
            <path
              d="M6.99968 1.50839L10.4496 4.95835L9.62468 5.78331L7.58301 3.74164L7.58301 9.33335H6.41634L6.41634 3.74164L4.37468 5.78331L3.54972 4.95835L6.99968 1.50839ZM2.62467 8.16668V11.0834H11.3747V8.16668H12.5413V12.25H1.45801V8.16668H2.62467Z"
              fill="#4E5969"
            />
          </svg>
        </el-button>
      </div>

      <!-- 按物料查看表格 -->
      <el-table
        v-if="viewMode === 'material'"
        :data="filteredMaterialList"
        style="width: 100%"
        class="editable-table award-container"
        v-loading="loading"
        :span-method="objectSpanMethod"
        size="small"
      >
        <template
          v-for="item in dynamicColumn"
          :key="item.prop || item.label"
        >
          <template v-if="item.children?.length">
            <el-table-column
              :key="item.prop"
              :label="item.label"
              :prop="item.prop"
              :min-width="item.width"
              :show-overflow-tooltip="item.showOverflowTooltip"
            >
              <el-table-column
                v-for="child in item.children"
                :key="child.prop"
                :label="child.label"
                :prop="child.prop"
                :min-width="child.width"
                :show-overflow-tooltip="child.showOverflowTooltip"
              />
            </el-table-column>
          </template>
          <el-table-column
            v-else
            :key="item.prop || item.label"
            :label="item.label"
            :prop="item.prop"
            :min-width="item.width"
            :show-overflow-tooltip="item.showOverflowTooltip"
          >
          </el-table-column>
        </template>
        <el-table-column
          label="是否中标"
          prop="awarded"
          width="100"
          fixed="right"
        >
          <template #default="{ row }">
            <el-checkbox
              :model-value="row.awarded === 1"
              @update:model-value="updateAwardStatus(row, $event)"
              :disabled="isEditMode"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="中标数量"
          prop="awardedQuantity"
          min-width="200"
          fixed="right"
        >
          <template #default="{ row }">
            <el-input
              style="width: 100%"
              placeholder="请输入"
              v-model="row.awardedQuantity"
              type="number"
              :min="0.01"
              :step="0.01"
              :precision="2"
              :disabled="isEditMode || row.awarded !== 1"
              @input="handleAwardedQuantityInput($event, row)"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="中标账期"
          prop="projectPaymentId"
          min-width="200"
          fixed="right"
        >
          <template #default="{ row }">
            <el-select
              v-if="!(isEditMode || row.awarded !== 1)"
              v-model="row.projectPaymentId"
              placeholder="请选择"
              @change="handleProjectPaymentChange(row)"
            >
              <el-option
                v-for="item in row.paymentList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div v-else>
              {{ row.paymentList.find((item: any) => item.value === row.projectPaymentId)?.label ?? '--' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="中标价格"
          prop="bidAmount"
          min-width="200"
          fixed="right"
        >
          <template #default="{ row }">
            <div>{{ row.bidAmount ?? '--' }}</div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 按供应商查看表格 -->
      <el-table
        v-if="viewMode === 'supplier'"
        :data="filteredSupplierList"
        style="width: 100%"
        v-loading="loading"
        class="editable-table"
      >
        <el-table-column
          v-for="item in SUPPLIER_COLUMN"
          :key="item.prop"
          :label="item.label"
          :prop="item.prop"
          :type="item.type"
          :width="item.width || undefined"
        >
          <template #default="{ row }">
            <template v-if="item.type === 'index'">
              {{ row?._index }}
            </template>
            <template v-else-if="item.prop === 'lookUp'">
              <el-button
                type="text"
                @click="handleViewRegister(row)"
                >查看</el-button
              >
            </template>
            <template v-else-if="item.prop === 'action'">
              <el-button
                type="text"
                @click="handleViewDetail(row)"
                >报价明细</el-button
              >
            </template>
            <template v-else>
              {{ row?.[item.prop] || '--' }}
            </template>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 定标公告 -->
    <div class="award-announcement-section">
      <div class="section-header">
        <div class="header-title">定标公告</div>
      </div>

      <el-form
        ref="announcementFormRef"
        :model="announcementForm"
        :rules="announcementRules"
        label-width="120px"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item
              label="引用模版"
              prop="template"
            >
              <el-select
                v-model="announcementForm.template"
                placeholder="请选择公告模版"
                @change="handleTemplateChange"
              >
                <el-option
                  v-for="template in templateOptions"
                  :key="template.id"
                  :label="template.templateName"
                  :value="template.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item
          v-if="isEditMode"
          label="公告内容"
        >
          <el-button
            type="text"
            @click="handleEditContent"
            class="edit-button"
          >
            <el-icon class="mr-[6px]"><edit-pen /></el-icon>
            查看
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 定标附件 -->
    <div class="award-attachments-section">
      <div class="section-header">
        <div class="header-title">定标附件</div>
      </div>

      <div class="attachment-form">
        <el-form label-width="120px">
          <el-row>
            <el-col
              v-if="!isEditMode"
              :span="12"
            >
              <el-form-item label="定标附件">
                <!-- 编辑模式：显示上传组件 -->
                <div class="upload-wrapper">
                  <YunUpload
                    v-model="awardFileList"
                    @change="handelUploadFile"
                    :limit="5"
                    :multiple="true"
                  ></YunUpload>
                </div>
              </el-form-item>
            </el-col>

            <el-col
              v-if="isEditMode"
              :span="4"
            >
              <div class="flex gap-4 mt-3">
                <div class="file-label">定标结果附件：</div>
                <div
                  class="flex flex-col gap-2"
                  v-if="awardFileList.length > 0"
                >
                  <div
                    class="flex gap-4 items-center"
                    v-for="(item, index) in awardFileList"
                    :key="index"
                  >
                    <div
                      class="file-item-label"
                      @click="handleDownload(item)"
                    >
                      {{ item.fileName || item.name }}
                    </div>
                  </div>
                </div>
                <div
                  v-else
                  class="no-files"
                >
                  <span>暂无附件</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <!-- 审批设置 -->
    <div class="award-approval-section">
      <div class="section-header">
        <div class="header-title">审批设置</div>
      </div>

      <div class="approval-form">
        <el-form
          ref="approvalFormRef"
          :model="approvalData"
          :rules="approvalFormRules"
          label-width="120px"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item
                label="发起审批"
                prop="specialProcessExecutorList"
              >
                <div class="flex items-center gap-2">
                  <el-radio-group
                    v-model="approvalData.approvalType"
                    @change="handleApprovalTypeChange"
                  >
                    <el-radio :label="0">系统自动发起</el-radio>
                    <el-radio :label="1">指定审批人</el-radio>
                  </el-radio-group>
                  <UserSelector
                    v-model="approvalData.specialProcessExecutorList"
                    v-show="approvalData.approvalType === 1"
                    :multiple="true"
                    style="width: auto !important"
                    placeholder="请选择审批人"
                    @update:model-value="handleApprovalDataChange"
                  />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <!-- 定标备注 -->
    <div class="award-remark-section">
      <div class="section-header">
        <div class="header-title">定标备注</div>
      </div>

      <el-row>
        <el-col :span="12">
          <el-form label-width="120px">
            <el-form-item label="定标备注">
              <el-input
                v-model="remarkInfo.remark"
                type="textarea"
                :rows="5"
                placeholder="请输入定标说明"
                show-word-limit
              />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </div>

  <div
    v-if="!isEditMode && !props.isChangeMode && isProjectLeader"
    class="form-actions-wrapper"
  >
    <div class="form-actions">
      <el-button
        type="primary"
        :loading="saving"
        @click="handleSubmit"
      >
        {{ '生成定标公告' }}
      </el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </div>

  <div
    v-if="currentAward?.awardReportStatus === 'APPROVING' && !props.isChangeMode && isProjectLeader"
    class="form-actions-wrapper"
  >
    <div class="form-actions">
      <el-button
        type="primary"
        @click="handleRevoke"
      >
        {{ '撤销审批' }}
      </el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </div>

  <!-- 历史报价抽屉 -->
  <HistoryPriceDrawer ref="historyPriceDrawerRef" />

  <QuotationDetail
    v-model:visible="detailDrawerVisible"
    :row="currentRow"
  ></QuotationDetail>

  <!-- 公告内容编辑抽屉 -->
  <AnnouncementEditor
    ref="announcementEditorRef"
    :readonly="isEditMode"
    @save="handleSaveContent"
  />

  <!-- 报名资料 -->
  <AuditDrawer
    v-model:visible="auditDrawerVisible"
    :row="currentRow"
    :btnType="BTN_O_TYPE.DETAIL"
  />
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { debounce } from 'lodash-es';
import { ElMessage, ElMessageBox } from 'yun-design';
import Decimal from 'decimal.js';
import ExcelJS from 'exceljs';

// 高精度计算工具函数
const precisionMath = {
  // 乘法
  multiply: (a: number | string, b: number | string): number => {
    return new Decimal(a || 0).mul(new Decimal(b || 0)).toNumber();
  },
  // 加法
  add: (a: number | string, b: number | string): number => {
    return new Decimal(a || 0).add(new Decimal(b || 0)).toNumber();
  },
  // 减法
  subtract: (a: number | string, b: number | string): number => {
    return new Decimal(a || 0).sub(new Decimal(b || 0)).toNumber();
  },
  // 除法
  divide: (a: number | string, b: number | string): number => {
    if (Number(b) === 0) return 0;
    return new Decimal(a || 0).div(new Decimal(b)).toNumber();
  },
  // 保留指定位数小数
  toFixed: (value: number | string, decimals: number = 2): string => {
    return new Decimal(value || 0).toFixed(decimals);
  },
};
import StageTabs from '@/views/procurementSourcing/biddingProcess/components/bidding/StageTabs.vue';
import HistoryPriceDrawer from './components/HistoryPriceDrawer.vue';
import AnnouncementEditor from './components/AnnouncementEditor.vue';
import YunUpload from '@/components/YunUpload/index.vue';
import UserSelector from '@/components/UserSelector/index.vue';
import { getTemplateList, queryMaterialList, submitAwardReview } from '../../../api/award';
import { getTemplateDetail } from '../../../api/announcement';
import { templateContentFormatter } from '../../announcement/ProcurementDocument/templateFormatter';
import { batchAwardNotice, type BatchAwardNoticeItem } from '../../../api/WinnerNotification';
import type {
  QueryMaterialParams,
  MaterialInfo,
  MaterialTableRow,
  TemplateOption,
  SubmitAwardReviewData,
  TenderBidPublicityDetail,
} from '../../../types/award';
import downloadUrlFile from '@/utils/downloadUrl.js';
import moment from 'moment';
import { useBiddingStore, useAwardStore } from '@/views/procurementSourcing/biddingProcess/stores';
import QuotationDetail from '@/views/procurementSourcing/biddingProcess/components/BidEvaluation/online/components/QuotationDetail/index.vue';
// import QuotationDetailDrawer from '@/views/procurementSourcing/biddingProcess/components/bidding/QuotationManagement/QuotationDetailDrawer.vue';
import { getNoticeChangeDetailInfo } from '@/api/purchasing/noticeChange';
import { queryQuoteListBySupplier } from '@/api/purchasing/bid';
import { BTN_O_TYPE, SUPPLIER_COLUMN } from '@/views/procurementSourcing/biddingProcess/constants';
import AuditDrawer from '@/views/procurementSourcing/biddingProcess/components/bidding/DocumentReview/AuditDrawer.vue';
import { useDynamicTable } from '@/views/procurementSourcing/biddingProcess/hooks/useDynamicTable.jsx';
import { useExcel } from '@/views/procurementSourcing/biddingProcess/hooks/useExcel.jsx';
import { cancelApprovePlan } from '@/api/purchasing/plan';

const { dynamicColumn, tableData, setDynamicColumn, handleTableData, objectSpanMethod } = useDynamicTable();
const { exportToExcel } = useExcel();

// 定义组件 Props
interface Props {
  isChangeMode?: boolean; // 是否为变更模式
  isViewMode?: boolean; // 是否为查看模式
  sectionId?: number; // 标段ID，当传入时会隐藏StageTabs
}

const props = withDefaults(defineProps<Props>(), {
  isChangeMode: false,
  isViewMode: false,
  sectionId: undefined,
});

const emit = defineEmits<{
  (e: 'on-success'): void;
  (e: 'change-submit', data: { content: string; submitData: any }): void;
}>();

const biddingStore = useBiddingStore();
const awardStore = useAwardStore();
const detailDrawerVisible = ref(false);
const auditDrawerVisible = ref(false);
const currentRow = ref({
  quoteRoundCount: '', //轮次
  sectionId: '', // 区段
  tenantSupplierId: '', // 供应商ID
});

const projectDetail = computed(() => biddingStore?.projectDetail);

const noticeInfo = computed(() => {
  return biddingStore?.noticeInfo;
});
const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);
const isProjectLeader = computed(() => biddingStore?.isProjectLeader);
const isZJWT = computed(() => biddingStore?.isZJWT);

const roundNum = computed(() => {
  const sectionId = materialFilters.sectionId;
  return noticeInfo.value?.quoteRoundVoList?.find((item: any) => item.sectionId === sectionId)?.currentQuoteRound;
});

// 保存状态
const saving = ref(false);

// 导出状态
const exportLoading = ref(false);

const approvalStatus = computed(() => {
  return awardStore.tenderBidDetail?.projectItemList?.find((item: any) => item.sectionId === projectInfo.sectionId)?.awardReportStatus || '';
});

const currentAward = computed(() => {
  return awardStore.tenderBidDetail?.projectItemList?.find((item: any) => item.sectionId === projectInfo.sectionId);
});

// 页面状态：编辑模式还是新增模式 - 从 store 获取
const isEditMode = computed(() => {
  if (props.isViewMode) {
    return false;
  }

  const target = awardStore.tenderBidDetail?.projectItemList?.find((item: any) => item.sectionId === projectInfo.sectionId);

  if (!target?.awardReportContent) {
    return false;
  }

  if (!['APPROVE_REJECT', 'APPROVE_REVOKE'].includes(target.awardReportStatus) && !props.isChangeMode) {
    return true;
  }

  return false;
});

// 抽屉组件引用
const historyPriceDrawerRef = ref();
const announcementEditorRef = ref();

// 表单引用
const announcementFormRef = ref();
const approvalFormRef = ref();

// 模拟项目数据（实际应从路由参数或store获取）
const projectInfo = reactive({
  noticeId: noticeId.value, // 公告ID
  projectId: projectId.value, // 采购立项ID
  sectionId: props.sectionId || '', // 当前标段ID
});

// 定标基本信息
const awardInfo = reactive({
  awardPerson: projectDetail.value?.createBy || '',
  awardTime: '',
  projectName: projectDetail.value?.projectName || '',
  projectCode: projectDetail.value?.projectCode || '',
  procurementMethod: projectDetail.value?.procurementMethod || '',
  budgetAmount: projectDetail.value?.budgetAmount || 0,
});

const activeTabIndex = ref(0);
const viewMode = ref('material');

// 筛选条件
const materialFilters = reactive<QueryMaterialParams>({
  sectionId: projectInfo.sectionId, // 当前选中的标段ID
  roundNo: 1,
  materialName: '',
  supplierName: '',
});

const supplierFilters = reactive({
  supplierName: '',
});

// 原始物料数据
const materialData = ref<MaterialInfo[]>([]);

// 表格数据
const materialTableData = ref<MaterialTableRow[]>([]);
const supplierTableData = ref([]);
const loading = ref(false);

// 查看模式下的项目物料列表（用于获取中标数据）
const viewModeProjectItemList = ref<any[]>([]);

// 公告表单数据
const announcementForm = reactive({
  template: '',
  content: ``,
});

// 模板选项
const templateOptions = ref<TemplateOption[]>([]);

// 通知书模板选项
const notificationTemplateOptions = ref<TemplateOption[]>([]);
const notificationTemplateId = ref<string>('');
const notificationTemplateContent = ref<string>('');

// 定标附件文件列表
const awardFileList = ref<any[]>([]);

// 备注信息
const remarkInfo = reactive({ remark: '' });

// 审批数据
const approvalData = reactive({
  approvalType: 0, // 0: 系统自动发起, 1: 指定审批人
  specialProcessExecutorList: [], // 指定审批人列表
});

// 表单验证规则
const announcementRules = reactive({
  template: [{ required: true, message: '请选择公告模版', trigger: 'change' }],
});

// 审批表单验证规则
const approvalFormRules = reactive({
  specialProcessExecutorList: [
    {
      required: true,
      message: '请选择审批人',
      trigger: 'change',
      validator: (rule: any, value: any, callback: any) => {
        // 检查审批类型和审批人列表
        if (approvalData.approvalType === 1) {
          // 检查 approvalData.specialProcessExecutorList 而不是 value
          if (!approvalData.specialProcessExecutorList || approvalData.specialProcessExecutorList.length === 0) {
            callback(new Error('请选择审批人'));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
    },
  ],
});

// 计算属性 - 筛选后的物料列表
const filteredMaterialList = computed(() => {
  let filtered = [...materialTableData.value];

  // 按供应商名称模糊搜索
  if (materialFilters.supplierName) {
    filtered = filtered.filter((item) => item.supplierName?.toLowerCase().includes(materialFilters.supplierName?.toLowerCase() || ''));
  }

  return filtered;
});

// 计算属性 - 筛选后的供应商列表
const filteredSupplierList = computed(() => {
  let filtered = [...supplierTableData.value];

  // 按供应商名称模糊搜索
  if (supplierFilters.supplierName) {
    filtered = filtered.filter((item) => item.supplierName.toLowerCase().includes(supplierFilters.supplierName.toLowerCase()));
  }

  return filtered;
});

// 格式化价格
function formatPrice(price: number): string {
  return `¥${price.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

const handleRevoke = async () => {
  try {
    ElMessageBox.confirm('确定要撤销审批吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        cancelApprovePlan({ bizType: 'SRM_TENDER_BID_AUDIT', bizKey: `${projectId.value}#${noticeId.value}#${projectInfo.sectionId}` }).then(() => {
          ElMessage({
            type: 'success',
            message: '撤销审批成功！',
          });
          loadTenderBidDetail();
        });
      })
      .catch(() => {});
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤销审批失败:', error);
    }
  }
};

// 获取审批状态类型
function getApprovalStatusType(status: string): string {
  const typeMap: Record<string, string> = {
    TO_APPROVE: 'warning',
    APPROVING: 'warning',
    APPROVE: 'success',
    APPROVE_REJECT: 'danger',
    APPROVE_REVOKE: 'danger',
  };
  return typeMap[status] || '';
}

// 获取审批状态文本
function getApprovalStatusText(status: string): string {
  const textMap: Record<string, string> = {
    TO_APPROVE: '待审批',
    APPROVING: '审批中',
    APPROVE: '已审批',
    APPROVE_REJECT: '已驳回',
    APPROVE_REVOKE: '已撤销',
  };
  return textMap[status] || '未知';
}

// 供应商名称输入防抖处理
const handleSupplierNameInput = debounce(() => {
  loadMaterialData();
}, 500);

// 处理标段切换
function handleSectionChange(sectionId: number) {
  materialFilters.sectionId = sectionId;
  projectInfo.sectionId = sectionId; // 更新项目信息中的标段ID

  // 重新查询定标详情（因为标段ID变化了）
  loadTenderBidDetail();
}

const handleProjectPaymentChange = (row: any) => {
  const { awardedQuantity, projectPaymentId, paymentList } = row;
  const payment = paymentList.find((item: any) => item.value === projectPaymentId);
  if (awardedQuantity > 0 && projectPaymentId) {
    row.bidAmount = precisionMath.multiply(awardedQuantity, payment.price ?? 0);
  }
};

// 处理中标数量输入
const handleAwardedQuantityInput = (value: string | number, row: any) => {
  const numValue = Number(value);

  // 验证输入值
  if (numValue <= 0) {
    ElMessage.warning('中标数量必须大于0');
    row.awardedQuantity = '';
    return;
  }

  const { projectPaymentId, paymentList } = row;
  const payment = paymentList.find((item: any) => item.value === projectPaymentId);
  // 如果输入有效，自动计算中标价格
  if (row.projectPaymentId) {
    row.bidAmount = precisionMath.multiply(numValue, payment.price ?? 0);
  }
};

const handelUploadFile = (file: any[]) => {
  awardFileList.value = file;
};

// 处理审批类型变化
const handleApprovalTypeChange = (value: number) => {
  approvalData.approvalType = value;
  if (value === 0) {
    // 系统自动发起时清空审批人列表
    approvalData.specialProcessExecutorList = [];
  }

  // 触发审批人字段的验证
  nextTick(() => {
    if (approvalFormRef.value) {
      approvalFormRef.value.validateField('specialProcessExecutorList');
    }
  });
};

// 处理审批数据变化
const handleApprovalDataChange = (userIds: string[] | string) => {
  // update:modelValue 事件可能返回 userId 数组或单个 userId
  if (Array.isArray(userIds)) {
    approvalData.specialProcessExecutorList = userIds;
  } else if (typeof userIds === 'string' && userIds) {
    approvalData.specialProcessExecutorList = [userIds];
  } else {
    approvalData.specialProcessExecutorList = [];
  }

  // 触发审批人字段的验证
  nextTick(() => {
    if (approvalFormRef.value) {
      approvalFormRef.value.validateField('specialProcessExecutorList');
    }
  });
};

// 更新中标状态
function updateAwardStatus(row: MaterialTableRow, awarded: boolean) {
  row.awarded = awarded ? 1 : 0;
  if (!awarded) {
    row.awardedQuantity = 0;
  }
}

const changeViewMode = () => {
  if (viewMode.value === 'material') {
    loadMaterialData();
  } else {
    loadSupplierData();
  }
};

async function loadSupplierData() {
  try {
    loading.value = true;
    const { data } = await queryQuoteListBySupplier({
      sectionId: materialFilters.sectionId,
      roundNo: roundNum.value,
      noticeId: noticeId.value,
      projectId: projectId.value,
    });
    const list = data || [];
    supplierTableData.value = list?.map((row: any) => {
      return {
        ...row,
        count: row?.quoteMaterialCount || 0 / row?.totalMaterialCount || 0,
      };
    });
  } catch (e) {
    // 忽略错误，使用空数据
  } finally {
    loading.value = false;
  }
}

// 查询物料数据
async function loadMaterialData() {
  loading.value = true;
  try {
    if (!noticeId.value || !projectId.value) {
      return;
    }

    const params: QueryMaterialParams = {
      sectionId: materialFilters.sectionId,
      roundNo: roundNum.value,
      materialName: materialFilters.materialName || undefined,
      supplierName: materialFilters.supplierName || undefined,
      noticeId: noticeId.value,
      projectId: projectId.value,
      current: 1,
      size: 9999, // 传大页码获取全量数据
    };

    const { data, code } = await queryMaterialList(params);

    if (code === 0 && data.records) {
      materialData.value = data.records || [];
      setDynamicColumn(materialData.value);
      handleTableData(materialData.value);
      if (viewModeProjectItemList.value.length > 0) {
        viewModeProjectItemList.value.forEach((item: any) => {
          const matchedItem = tableData.value.find(
            (fItem: any) => fItem.tenantSupplierId === item.tenantSupplierId && fItem.materialCode === item.materialCode
          );
          if (matchedItem) {
            matchedItem.awardedQuantity = item.awardedQuantity;
            matchedItem.bidAmount = item.bidAmount;
            matchedItem.projectPaymentId = item.projectPaymentId;
            matchedItem.awarded = 1;
          }
        });
      }

      materialTableData.value = tableData.value;
    }
  } catch (error) {
  } finally {
    loading.value = false;
  }
}

// 报名资料
function handleViewRegister(row: any) {
  currentRow.value = { ...row, sectionId: projectInfo.sectionId };
  auditDrawerVisible.value = true;
}

// 查看详情
function handleViewDetail(row: any) {
  currentRow.value = { ...row, sectionId: projectInfo.sectionId };
  detailDrawerVisible.value = true;
}

// 数据校验函数
function validateRequiredData(): boolean {
  // 检查是否有中标物料
  const hasAwardedItems = materialTableData.value.some((item: any) => item.awarded === 1);
  if (!hasAwardedItems) {
    ElMessage.error('请至少选择一个物料进行中标');
    return false;
  }

  // 检查中标数量
  const invalidAwardedItems = materialTableData.value.filter(
    (item: any) => item.awarded === 1 && (!item.awardedQuantity || item.awardedQuantity <= 0)
  );
  if (invalidAwardedItems.length > 0) {
    ElMessage.error('请输入有效的中标数量');
    return false;
  }

  // 检查审批设置
  if (approvalData.approvalType === 1 && (!approvalData.specialProcessExecutorList || approvalData.specialProcessExecutorList.length === 0)) {
    ElMessage.error('请选择审批人');
    return false;
  }

  return true;
}

// 数据转换函数 - 将定标数据转换为模板格式
function transformAwardDataForTemplate() {
  // 获取中标项目
  const awardedItems = materialTableData.value.filter((item: any) => item.awarded === 1);

  // 按供应商分组
  const supplierGroups = awardedItems.reduce((acc: any, item: any) => {
    const key = item.supplierName || 'unknown';
    if (!acc[key]) {
      acc[key] = {
        supplierName: item.supplierName,
        contactPerson: item.contactPerson,
        contactPhone: item.contactPhone,
        materials: [],
        totalAmount: 0,
      };
    }
    acc[key].materials.push(item);
    acc[key].totalAmount = precisionMath.add(acc[key].totalAmount, item.quoteAmount);
    return acc;
  }, {} as Record<string, any>);

  const awardedSuppliers = Object.values(supplierGroups);
  const totalAwardAmount = awardedSuppliers.reduce((sum: number, supplier: any) => precisionMath.add(sum, supplier.totalAmount), 0);

  return {
    // 项目基本信息
    noticeId: projectInfo.noticeId,
    projectId: projectInfo.projectId,
    projectName: awardInfo.projectName || '',
    projectCode: awardInfo.projectCode || '',
    procurementMethod: awardInfo.procurementMethod || '',
    budgetAmount: awardInfo.budgetAmount || 0,

    // 定标信息
    awardPerson: awardInfo.awardPerson || projectDetail.value?.createBy || '',
    awardTime: moment().format('YYYY-MM-DD HH:mm:ss'),
    awardAmount: totalAwardAmount,
    formattedAwardAmount: formatPrice(totalAwardAmount),
    savingsAmount: precisionMath.subtract(awardInfo.budgetAmount, totalAwardAmount),
    formattedSavingsAmount: formatPrice(precisionMath.subtract(awardInfo.budgetAmount, totalAwardAmount)),

    // 中标供应商信息
    awardedSuppliers,
    totalSuppliers: awardedSuppliers.length,

    // 中标物料清单
    awardedMaterials: awardedItems.map((item: any, index: number) => ({
      index: index + 1,
      materialCode: item.materialCode,
      materialName: item.materialName,
      specModel: item.specModel,
      unit: item.unit,
      awardedQuantity: item.awardedQuantity,
      quotePrice: item.quotePrice,
      quoteAmount: item.quoteAmount,
      formattedPrice: formatPrice(item.quotePrice || 0),
      formattedAmount: formatPrice(item.quoteAmount || 0),
      supplierName: item.supplierName,
      deliveryPeriod: item.deliveryPeriod,
      remark: item.remark,
    })),

    // 时间信息
    generateTime: moment().format('YYYY-MM-DD HH:mm:ss'),
    currentDate: moment().format('YYYY年MM月DD日'),
  };
}

// 处理编辑公告内容
async function handleEditContent() {
  try {
    // 编辑模式：直接显示已保存的内容
    if (isEditMode.value) {
      if (!announcementForm.content) {
        ElMessage.error('暂无公告内容');
        return;
      }

      // 直接打开富文本编辑器，显示已保存的内容
      announcementEditorRef.value?.show(announcementForm.content, '查看定标公告');
      return;
    }
  } catch (error) {}
}

// 处理保存公告内容
async function handleSaveContent(content: string) {
  if (isEditMode.value) {
    return;
  }

  // 变更模式下，通过 emit 通知父组件处理
  if (props.isChangeMode) {
    // 1. 先校验数据
    if (!validateRequiredData()) {
      return;
    }

    // 2. 检查是否选择了模板
    if (!announcementForm.template) {
      ElMessage.error('请先选择定标公告模板');
      return;
    }

    // 3. 更新公告内容
    announcementForm.content = content;

    // 4. 验证其他表单
    const isValid = await validateForm();
    if (!isValid) {
      return;
    }

    // 5. 组装提交数据（使用相同的转换器）
    const submitData = await assembleSubmitData();

    // 6. 通知父组件处理变更提交
    emit('change-submit', {
      content,
      submitData,
    });
    return;
  }

  // 原有的定标模式逻辑
  // 1. 先校验数据
  if (!validateRequiredData()) {
    return;
  }

  // 2. 检查是否选择了模板
  if (!announcementForm.template) {
    ElMessage.error('请先选择定标公告模板');
    return;
  }

  try {
    saving.value = true;

    // 3. 更新公告内容
    announcementForm.content = content;

    // 4. 验证其他表单
    const isValid = await validateForm();
    if (!isValid) {
      return;
    }
    // 5. 组装提交数据
    const submitData = await assembleSubmitData();

    // 6. 调用接口保存定标结果
    const { winnerNotifications, ...awardData } = submitData;
    const { code, msg } = await submitAwardReview(awardData);

    if (code === 0) {
      ElMessage.success(isEditMode.value ? '定标信息更新成功' : '定标公告保存成功');
      // 7. 如果有中标通知书数据，批量保存通知书
      if (winnerNotifications && winnerNotifications.length > 0) {
        try {
          const notificationResult = await batchAwardNotice(winnerNotifications);
          if (notificationResult.code === 0) {
            ElMessage.success('中标通知书已生成');
          } else {
            ElMessage.warning(`中标通知书生成失败: ${notificationResult.msg}`);
          }
        } catch (error) {
          ElMessage.warning('中标通知书生成失败');
        }
      }
      emit('on-success');
      await awardStore.getTenderBidDetail(noticeId.value, projectId.value, true);
      await loadTenderBidDetail();
    } else {
      ElMessage.error(msg || (isEditMode.value ? '更新失败' : '保存失败'));
    }
  } catch (error) {
  } finally {
    saving.value = false;
  }
}

// 处理模板变更（参考原文件逻辑）
async function handleTemplateChange() {
  if (!announcementForm.template) return;

  try {
    const { data } = await getTemplateDetail(announcementForm.template);
    announcementForm.content = data.content;
  } catch (e) {}
}

// 验证表单
async function validateForm(): Promise<boolean> {
  try {
    // 验证公告表单
    if (announcementFormRef.value) {
      await announcementFormRef.value.validate();
    }

    // 验证是否有中标物料
    const hasAwardedItems = materialTableData.value.some((item: any) => item.awarded === 1);
    if (!hasAwardedItems) {
      ElMessage.error('请至少选择一个物料进行中标');
      return false;
    }

    // 验证中标数量
    const invalidAwardedItems = materialTableData.value.filter(
      (item: any) => item.awarded === 1 && (!item.awardedQuantity || item.awardedQuantity <= 0)
    );
    if (invalidAwardedItems.length > 0) {
      ElMessage.error('请输入有效的中标数量');
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
}

// 组装提交数据
async function assembleSubmitData(): Promise<SubmitAwardReviewData & { winnerNotifications?: BatchAwardNoticeItem[] }> {
  // 筛选中标的物料项目
  const awardedItems = materialTableData.value.filter((item: any) => item.awarded === 1);
  const procurementItemList = awardedItems.map((item: any) => ({
    id: item.projectItemId,
    projectId: projectInfo.projectId,
    sectionId: projectInfo.sectionId,
    tenantSupplierId: item.tenantSupplierId || 0,
    supplierName: item.supplierName,
    materialCode: item.materialCode,
    materialName: item.materialName,
    specModel: item.specModel,
    unit: item.unit,
    requiredQuantity: item.requiredQuantity,
    remark: item.remark || '',
    awardedQuantity: item.awardedQuantity,
    isBid: true,
    bidAmount: item.bidAmount,
    projectPaymentId: item.projectPaymentId,
  }));

  // 组装附件信息
  const awardAttachments = awardFileList.value.map((file: any) => ({
    fileName: file.fileName || file.name,
    filePath: file.filePath || file.url || '',
    fileType: file.fileType || file.type || '',
    fileSize: file.fileSize || file.size || 0,
  }));

  // 为每个中标供应商生成中标通知书
  let winnerNotifications: BatchAwardNoticeItem[] = [];

  if (notificationTemplateId.value) {
    try {
      // 将物料维度数据转换为供应商维度数据
      const supplierData = transformMaterialDataToSupplierData(materialTableData.value);

      // 为每个供应商生成通知书
      const notificationPromises = supplierData.map(async (supplier: any) => {
        const notificationContent = await generateNotificationForSupplier(supplier);

        return {
          noticeId: projectInfo.noticeId,
          projectId: projectInfo.projectId,
          sectionId: projectInfo.sectionId,
          templateId: parseInt(notificationTemplateId.value),
          tenantSupplierId: supplier.tenantSupplierId,
          supplierName: supplier.supplierName,
          noticeContent: notificationContent,
        } as BatchAwardNoticeItem;
      });

      winnerNotifications = await Promise.all(notificationPromises);
    } catch (error) {
      // 生成中标通知书失败，不影响定标结果的提交
    }
  }

  return {
    noticeId: projectInfo.noticeId,
    projectId: projectInfo.projectId,
    awardTemplateId: parseInt(announcementForm.template),
    awardReportContent: announcementForm.content,
    attachmentInfos: awardAttachments,
    awardRemark: remarkInfo.remark,
    projectItemList: procurementItemList,
    awardNoticeReqList: winnerNotifications, // 添加中标通知书数据
    approvalType: approvalData.approvalType,
    specialProcessExecutorList: approvalData.specialProcessExecutorList,
  };
}

// 处理提交（只处理数据校验和模板转换，然后打开编辑器）
async function handleSubmit() {
  // 1. 先校验数据
  if (!validateRequiredData()) {
    return;
  }

  // 2. 验证审批表单
  if (approvalFormRef.value) {
    try {
      await approvalFormRef.value.validate();
    } catch (error) {
      ElMessage.error('请完善审批设置');
      return;
    }
  }

  // 3. 检查是否选择了模板
  if (!announcementForm.template) {
    ElMessage.error('请先选择定标公告模板');
    return;
  }

  try {
    // 3. 转换数据为模板格式
    const templateData = transformAwardDataForTemplate();

    // 4. 获取模板内容（如果还没有的话）
    if (!announcementForm.content || announcementForm.content.includes('<h2>XDMY-LYMG')) {
      const { data } = await getTemplateDetail(announcementForm.template);
      announcementForm.content = data.content;
    }

    // 5. 使用模板格式化器处理内容
    const formattedContent = templateContentFormatter(announcementForm.content, templateData, {
      defaultValue: '-',
      keepUndefinedVariables: false,
      removeEmptyLoops: true,
      removeEmptyTags: true,
      debug: true, // 调试模式，便于查看
    });

    // 6. 打开富文本编辑器进行最终编辑和保存
    announcementEditorRef.value?.show(formattedContent, '编辑定标公告');
  } catch (error) {}
}

// 处理取消
function handleCancel() {
  emit('on-success');
}

// 查询定标详情 - 使用 store
async function loadTenderBidDetail() {
  try {
    if (!noticeId.value || !projectId.value) {
      return;
    }

    const service = props.isViewMode ? getNoticeChangeDetailInfo : awardStore.loadForAwardResult;
    const params = {
      noticeId: noticeId.value,
      projectId: projectId.value,
      changeTypeEnum: 'CHANGE_BID',
    };

    const result = props.isViewMode ? await service(params) : await service(noticeId.value, projectId.value, true);

    if (result.data) {
      announcementForm.content = result.data.awardReportContent;

      // 查看模式下保存项目物料列表
      if (result.data.projectItemList) {
        viewModeProjectItemList.value = result.data.projectItemList;
      }

      if (isEditMode.value) {
        result.data.projectItemList = result.data?.projectItemList?.map((item: any) => ({ ...item, createByName: result.data.createByName }));
        fillComponentSpecificData(
          props.isViewMode ? result.data : result.data?.projectItemList?.find((item: any) => item.sectionId === projectInfo.sectionId)
        );
      }

      // 加载物料数据（包含中标信息）
      if (viewMode.value === 'material') {
        loadMaterialData();
      } else {
        loadSupplierData();
      }
    }
  } catch (error) {}
}

// 回填组件特有数据 (公告表单数据已在 store 中处理)
function fillComponentSpecificData(data: TenderBidPublicityDetail) {
  // 回填定标基本信息
  awardInfo.awardPerson = data.createByName || '';
  awardInfo.awardTime = data.awardReportTime || '';
  announcementForm.template = data.awardTemplateId || '';
  // 回填定标附件
  if (data.awardAttachments && data.awardAttachments.length > 0) {
    awardFileList.value = data.awardAttachments.map((file) => ({
      fileName: file.fileName,
      filePath: file.filePath,
      fileType: file.fileType,
      fileSize: file.fileSize,
      name: file.fileName,
      url: file.filePath,
      type: file.fileType,
      size: file.fileSize,
    }));
  }

  // 回填定标备注
  if (data.awardRemark) {
    remarkInfo.remark = data.awardRemark;
  }

  // 回填审批数据
  if (data.approvalType !== undefined) {
    approvalData.approvalType = data.approvalType;
  }
  if (data.specialProcessExecutorList) {
    approvalData.specialProcessExecutorList = data.specialProcessExecutorList;
  }
}

// 变更模式专用的验证和提交方法
const validateAwardData = async (): Promise<boolean> => {
  try {
    // 验证是否有中标物料
    if (!validateRequiredData()) {
      return false;
    }

    // 2. 检查是否选择了模板
    if (!announcementForm.template) {
      ElMessage.error('请先选择定标公告模板');
      return false;
    }

    handleSubmit();

    return false;
  } catch (error) {
    return false;
  }
};

const handleDownload = (item: any) => {
  downloadUrlFile(item.filePath, item.fileName);
};

// 导出按物料查看的数据
async function handleExportMaterial() {
  if (!filteredMaterialList.value || filteredMaterialList.value.length === 0) {
    ElMessage.warning('没有可导出的数据');
    return;
  }

  try {
    exportLoading.value = true;

    // 构建动态列配置，正确处理多级表头
    const buildExportColumns = (columns: any[]): any[] => {
      const result: any[] = [];

      columns.forEach((col: any) => {
        if (col.children && col.children.length > 0) {
          // 有子列的情况，创建父级列
          const parentColumn = {
            label: col.label,
            prop: col.prop,
            fieldName: col.label,
            fieldCode: col.prop,
            width: col.width || 120,
            children: col.children.map((child: any) => ({
              label: child.label,
              prop: child.prop,
              fieldName: child.label,
              fieldCode: child.prop,
              width: child.width || 120,
            })),
          };
          result.push(parentColumn);
        } else {
          // 没有子列的情况
          result.push({
            label: col.label,
            prop: col.prop,
            fieldName: col.label,
            fieldCode: col.prop,
            width: col.width || 120,
          });
        }
      });

      return result;
    };

    const exportColumns = [
      // 添加序号列
      {
        label: '序号',
        prop: '_index',
        fieldName: '序号',
        fieldCode: '_index',
        width: 80,
      },
      // 使用动态列（支持多级表头）
      ...buildExportColumns(dynamicColumn.value || []),
      // 添加中标相关列
      {
        label: '是否中标',
        prop: 'awarded',
        fieldName: '是否中标',
        fieldCode: 'awarded',
        width: 100,
      },
      {
        label: '中标数量',
        prop: 'awardedQuantity',
        fieldName: '中标数量',
        fieldCode: 'awardedQuantity',
        width: 120,
      },
      {
        label: '中标账期',
        prop: 'projectPaymentId',
        fieldName: '中标账期',
        fieldCode: 'projectPaymentId',
        width: 120,
      },
      {
        label: '中标价格',
        prop: 'bidAmount',
        fieldName: '中标价格',
        fieldCode: 'bidAmount',
        width: 120,
      },
    ];

    // 处理数据，添加序号和格式化中标状态
    const exportData = filteredMaterialList.value.map((item: any, index: number) => {
      const payment = item.paymentList?.find((p: any) => p.value === item.projectPaymentId);

      // 处理多级表头的数据
      const processedItem = { ...item };

      // 遍历动态列，确保所有子列数据都存在
      (dynamicColumn.value || []).forEach((col: any) => {
        if (col.children && col.children.length > 0) {
          // 父级列数据
          processedItem[col.prop] = processedItem[col.prop] || '--';

          // 子列数据
          col.children.forEach((child: any) => {
            processedItem[child.prop] = processedItem[child.prop] || '--';
          });
        } else {
          // 单级列数据
          processedItem[col.prop] = processedItem[col.prop] || '--';
        }
      });

      return {
        ...processedItem,
        _index: index + 1,
        awarded: item.awarded === 1 ? '是' : '否',
        projectPaymentId: payment?.label || '--',
        bidAmount: item.bidAmount ? precisionMath.toFixed(item.bidAmount, 2) : '--',
      };
    });

    // 使用 exportToExcel 函数导出
    const blob = await exportToExcel(exportData, exportColumns, {
      protectWorksheet: false,
    });

    // 生成文件名
    let fileName = `定标清单-按物料查看`;
    if (materialFilters.supplierName) {
      fileName += `-${materialFilters.supplierName}`;
    }
    fileName += `-${new Date().toLocaleDateString()}.xlsx`;

    // 下载文件
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败');
  } finally {
    exportLoading.value = false;
  }
}

// 导出按供应商查看的数据
async function handleExportSupplier() {
  if (!filteredSupplierList.value || filteredSupplierList.value.length === 0) {
    ElMessage.warning('没有可导出的数据');
    return;
  }

  try {
    exportLoading.value = true;
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('定标清单-按供应商查看');

    // 添加标题信息
    const titleRow = worksheet.getRow(1);
    titleRow.getCell(1).value = `定标清单 - 按供应商查看`;
    titleRow.font = { bold: true, size: 14 };
    worksheet.mergeCells('A1:I1');

    const infoRow = worksheet.getRow(2);
    let infoText = `导出时间: ${new Date().toLocaleString()}`;
    if (supplierFilters.supplierName) {
      infoText += ` | 供应商: ${supplierFilters.supplierName}`;
    }
    infoRow.getCell(1).value = infoText;
    infoRow.font = { size: 10, color: { argb: 'FF666666' } };
    worksheet.mergeCells('A2:I2');

    // 设置表头
    const headers = ['序号', '供应商名称', '联系人', '报价轮次', '报价/物料条数', '本轮报价总价', '最终报价IP'];

    // 添加表头
    const headerRow = worksheet.getRow(4);
    headers.forEach((header, index) => {
      headerRow.getCell(index + 1).value = header;
    });

    // 设置表头样式
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    // 添加数据行
    filteredSupplierList.value.forEach((item: any, index: number) => {
      const row = worksheet.getRow(index + 5);
      row.getCell(1).value = index + 1;
      row.getCell(2).value = item.supplierName || '';
      row.getCell(3).value = item.contactPerson || '';
      row.getCell(4).value = item.quoteRoundCount || '';
      row.getCell(5).value = item.count || '';
      row.getCell(6).value = typeof item.totalQuoteAmount === 'number' ? item.totalQuoteAmount : parseFloat(item.totalQuoteAmount) || 0;
      row.getCell(7).value = item.quoteIp || '';

      // 设置数字格式
      row.getCell(6).numFmt = '#,##0.00';
    });

    // 设置列宽
    worksheet.columns = [
      { width: 8 }, // 序号
      { width: 25 }, // 供应商名称
      { width: 12 }, // 联系人
      { width: 12 }, // 报价轮次
      { width: 15 }, // 报价/物料条数
      { width: 15 }, // 本轮报价总价
      { width: 15 }, // 最终报价IP
    ];

    // 添加边框
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      });
    });

    // 生成文件名
    let fileName = `定标清单-按供应商查看-${new Date().toLocaleDateString()}.xlsx`;

    // 导出文件
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('导出失败:', error);
  } finally {
    exportLoading.value = false;
  }
}

// 将物料维度数据转换为供应商维度数据
function transformMaterialDataToSupplierData(materialData: MaterialTableRow[]) {
  const awardedItems = materialData.filter((item: any) => item.awarded === 1);

  // 按供应商分组统计
  const supplierGroups = awardedItems.reduce((acc: any, item: any) => {
    const key = item.tenantSupplierId;
    if (!acc[key]) {
      acc[key] = {
        tenantSupplierId: item.tenantSupplierId,
        supplierName: item.supplierName,
        contactPerson: item.contactPerson,
        contactPhone: item.contactPhone,
        awardedMaterials: [],
        totalAwardedAmount: 0,
        totalAwardedQuantity: 0,
        awardedMaterialCount: 0,
      };
    }

    acc[key].awardedMaterials.push({
      materialCode: item.materialCode,
      materialName: item.materialName,
      specModel: item.specModel,
      unit: item.unit,
      awardedQuantity: item.awardedQuantity,
      quotePrice: item.quotePrice,
      quoteAmount: item.quoteAmount,
    });

    acc[key].totalAwardedAmount = precisionMath.add(acc[key].totalAwardedAmount, item.quoteAmount);
    acc[key].totalAwardedQuantity = precisionMath.add(acc[key].totalAwardedQuantity, item.awardedQuantity);
    acc[key].awardedMaterialCount += 1;

    return acc;
  }, {} as Record<string, any>);

  return Object.values(supplierGroups);
}

// 转换供应商数据为模板格式
function transformSupplierDataForTemplate(supplier: any) {
  return {
    // 项目基本信息
    projectName: awardInfo.projectName || '',
    projectCode: awardInfo.projectCode || '',
    procurementMethod: awardInfo.procurementMethod || '',
    noticeId: projectInfo.noticeId,
    projectId: projectInfo.projectId,

    // 供应商信息
    supplierName: supplier.supplierName,
    contactPerson: supplier.contactPerson,
    contactPhone: supplier.contactPhone,

    // 中标信息
    totalAwardedAmount: supplier.totalAwardedAmount,
    formattedTotalAmount: formatPrice(supplier.totalAwardedAmount || 0),
    awardedMaterialCount: supplier.awardedMaterialCount,
    totalAwardedQuantity: supplier.totalAwardedQuantity,

    // 中标物料列表
    awardedMaterials: supplier.awardedMaterials.map((material: any, index: number) => ({
      index: index + 1,
      materialCode: material.materialCode,
      materialName: material.materialName,
      specModel: material.specModel,
      unit: material.unit,
      awardedQuantity: material.awardedQuantity,
      quotePrice: material.quotePrice,
      quoteAmount: material.quoteAmount,
      formattedPrice: formatPrice(material.quotePrice || 0),
      formattedAmount: formatPrice(material.quoteAmount || 0),
    })),

    // 定标信息
    awardPerson: awardInfo.awardPerson || projectDetail.value?.createBy || '',
    awardTime: awardInfo.awardTime || moment().format('YYYY-MM-DD HH:mm:ss'),

    // 时间信息
    generateTime: moment().format('YYYY-MM-DD HH:mm:ss'),
    currentDate: moment().format('YYYY年MM月DD日'),
  };
}

// 为单个供应商生成中标通知书
async function generateNotificationForSupplier(supplier: any) {
  try {
    if (!notificationTemplateId.value) {
      return '';
    }

    // 构建单个供应商的模板数据
    const templateData = transformSupplierDataForTemplate(supplier);

    // 确保有模板内容
    if (!notificationTemplateContent.value) {
      await loadNotificationTemplateContent(notificationTemplateId.value);
    }

    // 使用模板格式化器处理内容
    const formattedContent = templateContentFormatter(notificationTemplateContent.value, templateData, {
      defaultValue: '-',
      keepUndefinedVariables: false,
      removeEmptyLoops: true,
      removeEmptyTags: true,
      debug: false,
    });

    return formattedContent;
  } catch (error) {
    // 生成供应商通知书失败，返回空内容
    return '';
  }
}

// 获取当前组件的数据用于变更提交
const getAwardData = () => {
  return {
    awardInfo: { ...awardInfo },
    announcementForm: { ...announcementForm },
    materialTableData: [...materialTableData.value],
    awardFileList: [...awardFileList.value],
    remarkInfo: { ...remarkInfo },
  };
};

// 获取通知书模板列表
async function getNotificationTemplateOptions() {
  const templateTypes = ['AWARD_NOTIFICATION']; // 尝试多种模板类型

  for (const type of templateTypes) {
    try {
      const { data } = await getTemplateList({
        type: type,
      });

      if (data?.records && data.records.length > 0) {
        notificationTemplateOptions.value = data.records;
        // 自动选择第一个模板
        if (!notificationTemplateId.value) {
          const defaultTemplate = data.records.find((item: any) => item.isDefault === 1);
          notificationTemplateId.value = defaultTemplate?.id || data.records[0]?.id || '';

          if (notificationTemplateId.value) {
            await loadNotificationTemplateContent(notificationTemplateId.value);
          }
        }
        return; // 成功获取到模板，退出循环
      }
    } catch (error) {
      // 继续尝试下一个类型
    }
  }
}

// 加载通知书模板内容
async function loadNotificationTemplateContent(templateId: string) {
  if (!templateId) return;

  try {
    const { data } = await getTemplateDetail(templateId);
    notificationTemplateContent.value = data.content || '';
  } catch (error) {
    // 加载失败时保持空内容
  }
}

const init = async () => {
  // 获取定标公告模板列表
  const { data } = await getTemplateList({
    type: 'AWARD_RESULT',
  });
  if (data?.records && data.records.length > 0) {
    templateOptions.value = data.records;
    // 如果不是编辑模式，设置默认模板
    if (!isEditMode.value && !props.isChangeMode) {
      announcementForm.template = data.records[0]?.id || '';
    }
  }

  if (props.sectionId) {
    handleSectionChange(props.sectionId);
  }

  // 获取通知书模板列表
  await getNotificationTemplateOptions();
};

// 暴露方法给父组件调用
defineExpose({
  validateAwardData,
  getAwardData,
  handleSaveContent,
  handleSubmit,
});

onMounted(() => {
  init();
});
</script>

<style lang="scss" scoped>
@import '../../../styles/collapse-panel.scss';

.award-info-section,
.award-list-section,
.award-announcement-section,
.award-attachments-section,
.award-approval-section,
.award-remark-section,
.award-approval-section {
  background: #fff;
  border-radius: 6px;
  margin-bottom: 12px;
  padding: 16px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
}

.award-announcement-section,
.award-remark-section,
.award-attachments-section,
.award-approval-section {
  .section-header {
    margin-bottom: 12px;
  }
}

.change-mode {
  .award-info-section,
  .award-list-section,
  .award-announcement-section,
  .award-attachments-section,
  .award-approval-section,
  .award-remark-section,
  .award-approval-section {
    margin-bottom: 0 !important;
    padding: 16px 16px 0 16px !important;
  }
}

.price-text {
  font-weight: 500;
  color: var(--Color-Primary-color-primary, #0069ff);
}

.savings-text {
  font-weight: 500;
  color: #52c41a;
}

.savings-rate {
  font-weight: 500;
  color: #52c41a;
}

.award-container {
  max-height: 400px;
  overflow-y: auto;
}

.award-info-content {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  flex-direction: row;
  gap: 8px;
}

.info-label {
  color: #86909c;
  font-size: 14px;
  line-height: 20px;
}

.info-value {
  color: #1d2129;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.attachment-form {
  :deep(.el-form-item--default) {
    margin-bottom: 0 !important;
    //width: 100%;
  }
}

.editable-table {
  :deep(.el-table__header) {
    th {
      background: var(--Color-Fill-fill-color-light, #f5f7fa);
      padding: 6px 0;
      border-bottom: 1px solid var(--Color-Border-border-color-light, #ebeef5);

      .cell {
        color: var(---el-text-color-regular, #505762);
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
      }
    }
  }

  :deep(.el-table__body) {
    tr {
      &:hover {
        background-color: #fff !important;

        td {
          background-color: #fff !important;
        }
      }

      td {
        border-bottom: 1px solid #ebeef5;
        padding: 6px 0;
      }
    }
  }

  // 确保 Element Plus 的 hover 类也被覆盖
  :deep(.el-table__row) {
    &:hover {
      background-color: #fff !important;

      td {
        background-color: #fff !important;
      }
    }

    &.hover-row {
      background-color: transparent !important;
    }

    td {
      &.hover-cell {
        background-color: transparent !important;
      }
    }
  }

  // 表格内的控件样式
  .table-select,
  .table-input {
    width: 100%;

    :deep(.el-input__wrapper) {
      background-color: transparent !important;
      border: 1px solid transparent !important;
      box-shadow: none !important;
      padding: 4px 8px;

      &:hover,
      &:focus {
        border-color: var(--Color-Primary-color-primary, #0069ff) !important;
        background-color: #fff !important;
      }
    }

    :deep(.el-input__inner) {
      border-radius: var(--Radius-border-radius-small, 2px) !important;
      background: var(--Color-Fill-fill-color-light, #f5f7fa) !important;
      border: none !important;
      color: #1d2129 !important;
      font-size: 14px;
      height: 24px;

      &::placeholder {
        color: var(--Color-Text-text-color-regular, #4e5969);
        font-family: 'PingFang SC';
        font-size: 14px;
      }
    }
  }

  // 操作按钮样式
  .table-actions {
    display: flex;
    gap: 8px;
    align-items: center;

    .action-btn {
      padding: 4px 8px;
      height: 28px;
      font-size: 12px;
      border-radius: 4px;

      &.el-button--primary {
        background: var(--Color-Primary-color-primary, #0069ff);
        border-color: var(--Color-Primary-color-primary, #0069ff);

        &:hover {
          background: #1677ff;
          border-color: #1677ff;
        }
      }

      &.el-button--danger {
        background: #ff4d4f;
        border-color: #ff4d4f;

        &:hover {
          background: #ff7875;
          border-color: #ff7875;
        }
      }
    }
  }
}

.export-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 16px;
}

.filter-section {
  //margin-bottom: 16px;
  //padding: 16px;
  //background: #f9fafb;
  border-radius: 6px;
  //border: 1px solid #e5e7eb;
}

.filter-form {
  margin: 0;

  .el-form-item {
    margin-bottom: 0;
    margin-right: 16px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.content-editor-wrapper {
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  min-height: 120px;
  background: #fff;

  .content-preview {
    padding: 12px;
    max-height: 200px;
    overflow-y: auto;

    .preview-text {
      color: #606266;
      font-size: 14px;
      line-height: 1.5;
      word-break: break-word;
    }
  }

  .content-placeholder {
    padding: 12px;
    color: #c0c4cc;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 96px;
  }

  .edit-button {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 10;
  }

  &:hover {
    border-color: #c0c4cc;

    .edit-button {
      opacity: 1;
    }
  }
}

.form-actions-wrapper {
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  z-index: 1000;
  position: sticky;
  bottom: -20px;
  left: 0;
  right: 0;
  padding: 16px 24px;
  display: flex;
  gap: 12px;
  border-top: 1px solid var(--Color-Border-border-color-light, #e4e7ed);

  .form-actions {
    display: flex;
    justify-content: flex-start;
    gap: 12px;
    width: 100%;
  }
}

.award-text-primary {
  color: var(--Color-Text-text-color-primary, #1d2129);

  /* regular/extra-small */
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 600;
  line-height: 20px;
}

.file-label {
  color: var(--Color-Text-text-color-regular, #4e5969);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}

.file-item-label {
  color: var(--Color-Primary-color-primary, #0069ff);
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
}

.header-title {
  color: var(--Color-Text-text-color-primary, #1d2129);
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
  padding-left: 10px;
  position: relative;
  &::before {
    content: '';
    display: inline-block;
    width: 2px;
    height: 14px;
    background: var(--Color-Primary-color-primary, #0069ff);
    margin-right: 8px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}
</style>
