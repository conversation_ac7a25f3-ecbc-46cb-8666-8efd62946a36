<template>
  <yun-drawer
    v-model="showVisible"
    title="报价明细"
    size="X-large"
    :with-header="true"
    append-to-body
    destroy-on-close
    :show-footer="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    custom-class="bid-custom-drawer"
  >
    <div
      class="flex flex-col"
      v-loading="loading"
      style="height: 98%"
    >
      <!-- 报价轮次和基本信息 -->
      <div>
        <el-tabs
          v-model="activeTab"
          @tab-click="handleTabClick"
        >
          <el-tab-pane
            v-for="item in quotationRounds"
            :key="item.value"
            :label="item.label"
            :name="item.value"
          />
        </el-tabs>
      </div>
      <div
        class="other-info-wrapper"
        v-if="extraInfo"
      >
        <div class="flex items-center gap-2">
          <!-- <div class="item-label">第{{ numberToChinese(activeTab) }}次报价</div> -->
          <div class="item-block">
            <span class="label">报价时间:</span>
            <span class="value">{{ extraInfo?.quoteTime ? moment(extraInfo?.quoteTime).format('YYYY-MM-DD HH:mm:ss') : '--' }}</span>
          </div>
          <div class="item-block">
            <span class="label">报价IP:</span>
            <span class="value">{{ extraInfo?.quoteIp || '--' }}</span>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <div class="item-block">
            <span class="label">报价文件:</span>
            <template v-if="extraInfo?.quoteAttachments">
              <el-tooltip
                class="item"
                effect="dark"
                placement="top"
                :content="extraInfo?.quoteAttachments?.fileName"
              >
                <span class="value file-value">{{ extraInfo?.quoteAttachments?.fileName || '--' }}</span>
              </el-tooltip>
              <span
                class="download-btn"
                v-if="extraInfo?.quoteAttachments?.filePath"
              >
                <el-link
                  :href="extraInfo?.quoteAttachments?.filePath"
                  target="_blank"
                  type="primary"
                >
                  下载
                </el-link>
              </span>
            </template>
            <span
              class="value"
              v-else
              >--</span
            >
          </div>
          <div class="item-block">
            <span class="label">其他附件:</span>
            <template v-if="extraInfo?.otherAttachments">
              <el-tooltip
                class="item"
                effect="dark"
                placement="top"
                :content="extraInfo?.otherAttachments?.fileName"
              >
                <span class="value file-value">{{ extraInfo?.otherAttachments?.fileName }}</span>
              </el-tooltip>
              <span
                class="download-btn"
                v-if="extraInfo?.otherAttachments?.filePath"
              >
                <el-link
                  :href="extraInfo?.otherAttachments?.filePath"
                  target="_blank"
                  type="primary"
                >
                  下载
                </el-link>
              </span>
            </template>
            <span
              class="value"
              v-else
              >--</span
            >
          </div>
        </div>
      </div>
      <div class="need-hide-table-card flex-1">
        <yun-pro-table
          ref="proTableRef"
          v-model:pagination="pagination"
          v-model:filter-data="filterTableData"
          v-model:searchData="searchData"
          :table-columns="dynamicColumn"
          :search-fields="searchFields"
          :auto-height="true"
          :remote-method="remoteMethod"
          :table-props="tablePropsObj"
          :search-props="searchPropsObj"
          layout="whole"
          :default-fetch="false"
          :extra="{
            position: 'breaster',
          }"
        >
        </yun-pro-table>
      </div>
    </div>
  </yun-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { queryQuoteListByMaterial } from '@/api/purchasing/bid';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { numberToChinese } from '@/views/procurementSourcing/biddingProcess/utils';
import moment from 'moment';
// @ts-ignore
import { useDynamicTable } from '@/views/procurementSourcing/biddingProcess/hooks/useDynamicTable.jsx';
import { useColumns } from './hooks/useColumns';
import { useProTable } from '@ylz-use/core';
import { useUserRole } from '@/views/procurementSourcing/biddingProcess/utils';

const emits = defineEmits(['update:visible']);
const props = defineProps({
  isFixedRound: {
    type: Boolean,
    default: false,
  },
  visible: {
    type: Boolean,
    default: false,
  },
  row: {
    type: Object,
    default: () => null,
  },
});
const { isSupplier } = useUserRole();
const { searchFields, searchData } = useColumns();
const { dynamicColumn: dynamicColumnOrigin, tableData, setDynamicColumn, handleTableData, objectSpanMethod } = useDynamicTable();
const dynamicColumn = computed(() => {
  return [
    ...dynamicColumnOrigin.value,
    {
      prop: 'quoteIp',
      label: '投标IP',
      fieldName: '投标IP',
      fieldCode: 'quoteIp',
      width: 140,
    },
  ];
});
const activeTab = ref(0);
const quotationRounds = ref<any>([]);
const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);
const loading = ref(false);
const showVisible = ref(false);
const currentRow = ref<any>(null);
const ownSupplier = computed(() => biddingStore?.ownSupplier || {});
const tenantSupplierId = computed(() => {
  if (isSupplier.value) {
    return ownSupplier?.value?.id || null;
  }
  return currentRow.value?.tenantSupplierId || props?.row?.tenantSupplierId || null;
});
const sectionId = computed(() => {
  return currentRow.value?.sectionId || props?.row?.sectionId || null;
});
const quoteRoundCount = computed(() => {
  const r = currentRow.value || props?.row || {};
  const { currentQuoteRound, quoteRoundCount } = r;
  return currentQuoteRound || quoteRoundCount || 0;
});
// const isNewQuotation = computed(() => {
//   return activeTab.value === quotationRounds.value?.length;
// });

// 监听props.visible方式的场景
watch(
  () => props.visible,
  (val) => {
    showVisible.value = val
  }
);
watch(
  () => showVisible.value,
  (val) => {
    if (val) {
      emits('update:visible', true);
      initRoundData();
      setTimeout(reLoad, 100);
    } else {
      emits('update:visible', false);
    }
  }
);

const extraInfo = computed(() => {
  const res: any = tableData?.value?.[0] || {};
  const { supplierName, attachmentList, quoteTime, quoteIp } = res;
  const otherAttachments = attachmentList?.find((item: any) => item.businessType === 'QUOTE_OTHER_ATTACHMENT') || null;
  const quoteAttachments = attachmentList?.find((item: any) => item.businessType === 'QUOTE_ATTACHMENT') || null;
  return {
    supplierName,
    quoteTime,
    quoteIp,
    otherAttachments,
    quoteAttachments,
  };
});


async function initRoundData() {
  // 固定轮次只会有一轮报价
  if (props.isFixedRound) {
    const lastRound = currentRow.value?.roundNo || 0;
    activeTab.value = lastRound;
    quotationRounds.value = [
      {
        label: `第${numberToChinese(lastRound)}轮报价`,
        value: lastRound,
      },
    ];
    return;
  } else {
    quotationRounds.value = Array.from({ length: quoteRoundCount.value }, (_, i) => i + 1)
      .map((item) => {
        return {
          label: `第${numberToChinese(item)}次报价`,
          value: item,
        };
      })
      .reverse();
    activeTab.value = quotationRounds.value[0]?.value || null;
  }
}

const isNewRound = computed<any>(() => {
  if (props.isFixedRound) {
    return currentRow.value?.roundNo === activeTab.value;
  }
  return quoteRoundCount.value === activeTab.value
})


const { remoteMethod, tableProps, proTableRef, filterTableData, reLoad, pagination } = useProTable({
  apiFn: queryQuoteListByMaterial,
  async responseHandler(result: any) {
    const list: any = result?.data?.records || [];
    await setDynamicColumn();
    await handleTableData(list, isNewRound.value);
    return tableData.value || [];
  },
  // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
  customTotalHandler(data: any) {
    return data?.data?.total || data?.total;
  },
  paramsHandler(params: any) {
    const param = {
      sectionId: sectionId.value,
      tenantSupplierId: tenantSupplierId.value,
      noticeId: noticeId.value,
      projectId: projectId.value,
      roundNo: activeTab.value,
      ...params,
    };
    return param;
  },
  // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
  querysHandler(querys: any) {
    const querysData = {
      // ...querys,
      current: pagination.value.page,
      // size: 1000, // 先查询所有数据
      size: pagination.value.size || pagination.value.pageSize,
    };
    return querysData;
  },
  plugins: {
    config: {
      columns: [],
      searchFields: [],
    },
    list: ['SEARCH_PLUS'],
  },
});

function handleTabClick() {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
    reLoad();
  }, 300);
}

function show(row: any) {
  currentRow.value = row || null;
  activeTab.value = 0;
  showVisible.value = true;
}

const searchPropsObj = computed(() => ({
  showCollapseBtn: false,
  collapse: false,
  showResetBtn: false,
  showSubmitBtn: false,
  showOperation: false,
  quantity: 3,
}));

const tablePropsObj = computed(() => ({
  ...tableProps.value,
  'span-method': objectSpanMethod,
  stripe: true,
  border: true,
  headerCellStyle: {
    backgroundColor: '#f5f7fa',
    color: '#303133',
    fontWeight: 'bold',
    // height: '40px',
    borderColor: '#EBEEF5',
    'vertical-align': 'middle',
  },
  cellStyle: {
    padding: '0',
    // height: '40px',
    'vertical-align': 'middle',
    borderColor: '#EBEEF5',
  },
  rowStyle: {
    // height: '40px',
    borderColor: '#EBEEF5',
  },
  // rowHeight: 40,
}));

defineExpose({
  show,
});
</script>

<style scoped lang="scss">
@import '@/views/procurementSourcing/biddingProcess/styles/collapse-panel.scss';

.other-info-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // gap: 8px;
  // margin: 20px 0;
  // margin-bottom: 20px;
  // margin-top: 12px;
  .item-label {
    color: var(--Color-Text-text-color-primary, #1d2129);
    /* bold/medium */
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
    position: relative;
    padding-left: 10px;
    &::before {
      content: ' ';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 2px;
      height: 14px;
      background: var(--Color-Primary-color-primary, #0069ff);
    }
  }
  .item-block {
    display: inline-flex;
    align-items: center;
    border-radius: var(--Radius-border-radius-small, 2px);
    border: 1px solid var(--Color-Border-border-color-light, #e4e7ed);
    background: var(--color-white, #fff);
    height: 24px;
    padding: 2px 8px;
    box-sizing: border-box;
    gap: 8px;
    .label {
      color: var(--Color-Text-text-color-secondary, #86909c);
      /* regular/base */
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .value {
      color: var(--Color-Text-text-color-primary, #1d2129);
      /* regular/base */
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
    .file-value {
      max-width: 160px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: break-all;
    }
    .download-btn {
      color: var(--Color-Primary-color-primary, #0069ff);
      /* regular/base */
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }
}
</style>
